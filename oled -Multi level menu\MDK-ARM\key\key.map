Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f103xe.o(RESET) refers to startup_stm32f103xe.o(STACK) for __initial_sp
    startup_stm32f103xe.o(RESET) refers to startup_stm32f103xe.o(.text) for Reset_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler) for DMA1_Channel5_IRQHandler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.I2C1_EV_IRQHandler) for I2C1_EV_IRQHandler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f103xe.o(.text) refers to system_stm32f1xx.o(i.SystemInit) for SystemInit
    startup_stm32f103xe.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to btn_app.o(i.app_ebtn_init) for app_ebtn_init
    main.o(i.main) refers to scheduler.o(i.scheduler_init) for scheduler_init
    main.o(i.main) refers to ringbuffer.o(i.rt_ringbuffer_init) for rt_ringbuffer_init
    main.o(i.main) refers to u8g2_d_setup.o(i.u8g2_Setup_ssd1315_i2c_128x64_noname_f) for u8g2_Setup_ssd1315_i2c_128x64_noname_f
    main.o(i.main) refers to u8x8_display.o(i.u8x8_InitDisplay) for u8x8_InitDisplay
    main.o(i.main) refers to u8x8_display.o(i.u8x8_SetPowerSave) for u8x8_SetPowerSave
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to scheduler.o(i.scheduler_run) for scheduler_run
    main.o(i.main) refers to usart_app.o(.bss) for ringbuffer_pool
    main.o(i.main) refers to oled_app.o(i.u8g2_gpio_and_delay_stm32) for u8g2_gpio_and_delay_stm32
    main.o(i.main) refers to oled_app.o(i.u8x8_byte_hw_i2c) for u8x8_byte_hw_i2c
    main.o(i.main) refers to u8g2_setup.o(.constdata) for u8g2_cb_r0
    main.o(i.main) refers to oled_app.o(.bss) for u8g2
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dma.o(i.MX_DMA_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.MX_I2C1_Init) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for hi2c1
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for hdma_usart1_rx
    usart.o(i.MX_USART1_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for huart1
    usart.o(i.MX_USART1_UART_Init) refers to usart_app.o(.bss) for uart_rx_dma_buffer
    stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f1xx_it.o(i.I2C1_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) for HAL_I2C_EV_IRQHandler
    stm32f1xx_it.o(i.I2C1_EV_IRQHandler) refers to i2c.o(.bss) for hi2c1
    stm32f1xx_it.o(i.SysTick_Handler) refers to stm32f1xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f1xx_it.o(i.USART1_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f1xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f1xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Master_ADD10) for I2C_Master_ADD10
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE) for I2C_SlaveTransmit_TXE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_SlaveTransmit_BTF) for I2C_SlaveTransmit_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE) for I2C_SlaveReceive_RXNE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_SlaveReceive_BTF) for I2C_SlaveReceive_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnSTOPRequestThroughIT) for I2C_WaitOnSTOPRequestThroughIT
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f1xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnSTOPRequestThroughIT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_DeInit) refers to stm32f1xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(.data) for uwTickFreq
    stm32f1xx_hal.o(i.HAL_GetTick) refers to stm32f1xx_hal.o(.data) for uwTick
    stm32f1xx_hal.o(i.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.data) for uwTickFreq
    stm32f1xx_hal.o(i.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal.o(i.HAL_IncTick) refers to stm32f1xx_hal.o(.data) for uwTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for uwTickFreq
    stm32f1xx_hal.o(i.HAL_InitTick) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.data) for uwTickFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.constdata) for AHBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to stm32f1xx_hal_rcc.o(.constdata) for aPLLMULFactorTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal_rcc.o(i.RCC_Delay) for RCC_Delay
    stm32f1xx_hal_rcc.o(i.RCC_Delay) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc_ex.o(.constdata) for aPLLMULFactorTable
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping) refers to stm32f1xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe) for PWR_OverloadWfe
    stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to llushr.o(.text) for __aeabi_llsr
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData) for FLASH_OB_ProgramData
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.constdata) for AHBPrescTable
    key_app.o(i.key_read) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_app.o(i.key_task) refers to key_app.o(i.key_read) for key_read
    key_app.o(i.key_task) refers to key_app.o(.data) for key_val
    led_app.o(i.led_disp) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    led_app.o(i.led_disp) refers to led_app.o(.data) for temp_old
    led_app.o(i.led_disp) refers to led_app.o(.constdata) for led_pin_configs
    led_app.o(i.led_task) refers to led_app.o(i.led_disp) for led_disp
    led_app.o(i.led_task) refers to led_app.o(.data) for ucLed
    scheduler.o(i.scheduler_init) refers to scheduler.o(.data) for task_num
    scheduler.o(i.scheduler_run) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.scheduler_run) refers to scheduler.o(.data) for scheduler_task
    scheduler.o(.data) refers to led_app.o(i.led_task) for led_task
    scheduler.o(.data) refers to key_app.o(i.key_task) for key_task
    scheduler.o(.data) refers to btn_app.o(i.btn_task) for btn_task
    scheduler.o(.data) refers to usart_app.o(i.uart_task) for uart_task
    scheduler.o(.data) refers to oled_app.o(i.oled_task) for oled_task
    btn_app.o(i.app_ebtn_init) refers to ebtn.o(i.ebtn_init) for ebtn_init
    btn_app.o(i.app_ebtn_init) refers to ebtn.o(i.ebtn_set_config) for ebtn_set_config
    btn_app.o(i.app_ebtn_init) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    btn_app.o(i.app_ebtn_init) refers to ebtn.o(i.ebtn_combo_btn_add_btn_by_idx) for ebtn_combo_btn_add_btn_by_idx
    btn_app.o(i.app_ebtn_init) refers to btn_app.o(i.prv_btn_event) for prv_btn_event
    btn_app.o(i.app_ebtn_init) refers to btn_app.o(i.prv_btn_get_state) for prv_btn_get_state
    btn_app.o(i.app_ebtn_init) refers to btn_app.o(.data) for btns_combo
    btn_app.o(i.btn_task) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    btn_app.o(i.btn_task) refers to ebtn.o(i.ebtn_process) for ebtn_process
    btn_app.o(i.prv_btn_event) refers to usart_app.o(i.my_printf) for my_printf
    btn_app.o(i.prv_btn_event) refers to led_app.o(.data) for ucLed
    btn_app.o(i.prv_btn_event) refers to usart.o(.bss) for huart1
    btn_app.o(i.prv_btn_get_state) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    btn_app.o(.data) refers to btn_app.o(.constdata) for default_ebtn_param
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(i.rt_ringbuffer_put) for rt_ringbuffer_put
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to memseta.o(.text) for __aeabi_memclr4
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to usart_app.o(.bss) for uart_rx_dma_buffer
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for huart1
    usart_app.o(i.__aeabi_assert) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.__aeabi_assert) refers to usart.o(.bss) for huart1
    usart_app.o(i.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    usart_app.o(i.my_printf) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart_app.o(i.uart_task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    usart_app.o(i.uart_task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    usart_app.o(i.uart_task) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.uart_task) refers to memseta.o(.text) for __aeabi_memclr4
    usart_app.o(i.uart_task) refers to usart_app.o(.bss) for uart_ringbuffer
    usart_app.o(i.uart_task) refers to usart.o(.bss) for huart1
    oled_app.o(i.oled_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    oled_app.o(i.oled_printf) refers to oled.o(i.OLED_ShowStr) for OLED_ShowStr
    oled_app.o(i.oled_task) refers to u8g2_hvline.o(i.u8g2_SetDrawColor) for u8g2_SetDrawColor
    oled_app.o(i.oled_task) refers to u8g2_buffer.o(i.u8g2_ClearBuffer) for u8g2_ClearBuffer
    oled_app.o(i.oled_task) refers to u8g2_font.o(i.u8g2_DrawStr) for u8g2_DrawStr
    oled_app.o(i.oled_task) refers to u8g2_circle.o(i.u8g2_DrawCircle) for u8g2_DrawCircle
    oled_app.o(i.oled_task) refers to u8g2_buffer.o(i.u8g2_SendBuffer) for u8g2_SendBuffer
    oled_app.o(i.oled_task) refers to oled_app.o(.bss) for u8g2
    oled_app.o(i.u8g2_gpio_and_delay_stm32) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled_app.o(i.u8x8_byte_hw_i2c) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    oled_app.o(i.u8x8_byte_hw_i2c) refers to oled_app.o(.data) for buf_idx
    oled_app.o(i.u8x8_byte_hw_i2c) refers to oled_app.o(.bss) for buffer
    oled_app.o(i.u8x8_byte_hw_i2c) refers to i2c.o(.bss) for hi2c1
    ebtn.o(i.bit_array_cmp) refers to memcmp.o(.text) for memcmp
    ebtn.o(i.ebtn_combo_btn_add_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_combo_btn_add_btn) refers to ebtn.o(i.ebtn_combo_btn_add_btn_by_idx) for ebtn_combo_btn_add_btn_by_idx
    ebtn.o(i.ebtn_combo_btn_remove_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_combo_btn_remove_btn) refers to ebtn.o(i.ebtn_combo_btn_remove_btn_by_idx) for ebtn_combo_btn_remove_btn_by_idx
    ebtn.o(i.ebtn_combo_register) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_btn_by_key_id) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_btn_index_by_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_get_btn_index_by_btn_dyn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_get_btn_index_by_key_id) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_config) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_current_state) refers to ebtn.o(i.bit_array_assign) for bit_array_assign
    ebtn.o(i.ebtn_get_current_state) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_total_btn_cnt) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_init) refers to memseta.o(.text) for __aeabi_memclr4
    ebtn.o(i.ebtn_init) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_is_in_process) refers to ebtn.o(i.ebtn_is_btn_in_process) for ebtn_is_btn_in_process
    ebtn.o(i.ebtn_is_in_process) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_process) refers to ebtn.o(i.ebtn_get_current_state) for ebtn_get_current_state
    ebtn.o(i.ebtn_process) refers to ebtn.o(i.ebtn_process_with_curr_state) for ebtn_process_with_curr_state
    ebtn.o(i.ebtn_process_btn) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.ebtn_process_btn) refers to ebtn.o(i.prv_process_btn) for prv_process_btn
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_num_bits_set) for bit_array_num_bits_set
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_and) for bit_array_and
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_cmp) for bit_array_cmp
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.prv_process_btn) for prv_process_btn
    ebtn.o(i.ebtn_process_with_curr_state) refers to memseta.o(.text) for __aeabi_memclr4
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_and) for bit_array_and
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_cmp) for bit_array_cmp
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_or) for bit_array_or
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.ebtn_process_btn) for ebtn_process_btn
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.ebtn_process_btn_combo) for ebtn_process_btn_combo
    ebtn.o(i.ebtn_process_with_curr_state) refers to memcpya.o(.text) for __aeabi_memcpy4
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_register) refers to ebtn.o(i.ebtn_get_total_btn_cnt) for ebtn_get_total_btn_cnt
    ebtn.o(i.ebtn_register) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_set_config) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.prv_process_btn) refers to ebtn.o(i.ebtn_timer_sub) for ebtn_timer_sub
    ebtn.o(i.prv_process_btn) refers to ebtn.o(.bss) for ebtn_default
    ringbuffer.o(i.rt_ringbuffer_data_len) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_get) refers to usart_app.o(i.__aeabi_assert) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_get) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_get) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to usart_app.o(i.__aeabi_assert) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_init) refers to usart_app.o(i.__aeabi_assert) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_peek) refers to usart_app.o(i.__aeabi_assert) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_peek) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to usart_app.o(i.__aeabi_assert) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_put) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to usart_app.o(i.__aeabi_assert) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to usart_app.o(i.__aeabi_assert) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to usart_app.o(i.__aeabi_assert) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_reset) refers to usart_app.o(i.__aeabi_assert) for __aeabi_assert
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_Init) refers to oled.o(.data) for initcmd1
    oled.o(i.OLED_Set_Position) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for F8X16
    oled.o(i.OLED_ShowFloat) refers to cfcmple.o(.text) for __aeabi_cfcmple
    oled.o(i.OLED_ShowFloat) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowFloat) refers to fadd.o(.text) for __aeabi_fsub
    oled.o(i.OLED_ShowFloat) refers to ffixui.o(.text) for __aeabi_f2uiz
    oled.o(i.OLED_ShowFloat) refers to ffltui.o(.text) for __aeabi_ui2f
    oled.o(i.OLED_ShowFloat) refers to fmul.o(.text) for __aeabi_fmul
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHanzi) refers to oled.o(.constdata) for Hzk
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHzbig) refers to oled.o(.constdata) for Hzb
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowStr) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Write_cmd) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_Write_cmd) refers to i2c.o(.bss) for hi2c1
    oled.o(i.OLED_Write_data) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_Write_data) refers to i2c.o(.bss) for hi2c1
    mui.o(i.mui_Draw) refers to mui.o(i.mui_loop_over_form) for mui_loop_over_form
    mui.o(i.mui_Draw) refers to mui.o(i.mui_task_draw) for mui_task_draw
    mui.o(i.mui_EnterForm) refers to mui.o(i.mui_LeaveForm) for mui_LeaveForm
    mui.o(i.mui_EnterForm) refers to mui.o(i.mui_loop_over_form) for mui_loop_over_form
    mui.o(i.mui_EnterForm) refers to mui.o(i.mui_NextField) for mui_NextField
    mui.o(i.mui_EnterForm) refers to mui.o(i.mui_send_cursor_enter_msg) for mui_send_cursor_enter_msg
    mui.o(i.mui_EnterForm) refers to mui.o(i.mui_task_form_start) for mui_task_form_start
    mui.o(i.mui_EnterForm) refers to mui.o(i.mui_task_find_first_cursor_uif) for mui_task_find_first_cursor_uif
    mui.o(i.mui_GetCurrentCursorFocusPosition) refers to mui.o(i.mui_loop_over_form) for mui_loop_over_form
    mui.o(i.mui_GetCurrentCursorFocusPosition) refers to mui.o(i.mui_task_get_current_cursor_focus_position) for mui_task_get_current_cursor_focus_position
    mui.o(i.mui_GetCurrentFormId) refers to mui.o(i.mui_get_fds_char) for mui_get_fds_char
    mui.o(i.mui_GetSelectableFieldOptionCnt) refers to mui.o(i.mui_fds_get_token_cnt) for mui_fds_get_token_cnt
    mui.o(i.mui_GetSelectableFieldTextOption) refers to mui.o(i.mui_fds_get_nth_token) for mui_fds_get_nth_token
    mui.o(i.mui_GotoForm) refers to mui.o(i.mui_find_form) for mui_find_form
    mui.o(i.mui_GotoForm) refers to mui.o(i.mui_EnterForm) for mui_EnterForm
    mui.o(i.mui_GotoFormAutoCursorPosition) refers to mui.o(i.mui_GotoForm) for mui_GotoForm
    mui.o(i.mui_Init) refers to memseta.o(.text) for __aeabi_memclr4
    mui.o(i.mui_LeaveForm) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_LeaveForm) refers to mui.o(i.mui_loop_over_form) for mui_loop_over_form
    mui.o(i.mui_LeaveForm) refers to mui.o(i.mui_task_form_end) for mui_task_form_end
    mui.o(i.mui_NextField) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_NextField) refers to mui.o(i.mui_next_field) for mui_next_field
    mui.o(i.mui_NextField) refers to mui.o(i.mui_send_cursor_enter_msg) for mui_send_cursor_enter_msg
    mui.o(i.mui_PrevField) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_PrevField) refers to mui.o(i.mui_loop_over_form) for mui_loop_over_form
    mui.o(i.mui_PrevField) refers to mui.o(i.mui_send_cursor_enter_msg) for mui_send_cursor_enter_msg
    mui.o(i.mui_PrevField) refers to mui.o(i.mui_task_find_prev_cursor_uif) for mui_task_find_prev_cursor_uif
    mui.o(i.mui_PrevField) refers to mui.o(i.mui_task_find_last_cursor_uif) for mui_task_find_last_cursor_uif
    mui.o(i.mui_RestoreForm) refers to mui.o(i.mui_GotoForm) for mui_GotoForm
    mui.o(i.mui_SaveCursorPosition) refers to mui.o(i.mui_get_fds_char) for mui_get_fds_char
    mui.o(i.mui_SaveForm) refers to mui.o(i.mui_GetCurrentCursorFocusPosition) for mui_GetCurrentCursorFocusPosition
    mui.o(i.mui_SaveForm) refers to mui.o(i.mui_SaveFormWithCursorPosition) for mui_SaveFormWithCursorPosition
    mui.o(i.mui_SaveFormWithCursorPosition) refers to mui.o(i.mui_get_fds_char) for mui_get_fds_char
    mui.o(i.mui_SendSelect) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_SendSelectWithExecuteOnSelectFieldSearch) refers to mui.o(i.mui_loop_over_form) for mui_loop_over_form
    mui.o(i.mui_SendSelectWithExecuteOnSelectFieldSearch) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_SendSelectWithExecuteOnSelectFieldSearch) refers to mui.o(i.mui_send_cursor_enter_msg) for mui_send_cursor_enter_msg
    mui.o(i.mui_SendSelectWithExecuteOnSelectFieldSearch) refers to mui.o(i.mui_task_find_execute_on_select_field) for mui_task_find_execute_on_select_field
    mui.o(i.mui_SendValueDecrement) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_SendValueIncrement) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_fds_first_token) refers to mui.o(i.mui_fds_get_cmd_size_without_text) for mui_fds_get_cmd_size_without_text
    mui.o(i.mui_fds_first_token) refers to mui.o(i.mui_get_fds_char) for mui_get_fds_char
    mui.o(i.mui_fds_first_token) refers to mui.o(i.mui_fds_next_token) for mui_fds_next_token
    mui.o(i.mui_fds_get_cmd_size) refers to mui.o(i.mui_fds_get_cmd_size_without_text) for mui_fds_get_cmd_size_without_text
    mui.o(i.mui_fds_get_cmd_size) refers to mui.o(i.mui_get_fds_char) for mui_get_fds_char
    mui.o(i.mui_fds_get_cmd_size) refers to mui.o(i.mui_fds_parse_text) for mui_fds_parse_text
    mui.o(i.mui_fds_get_cmd_size_without_text) refers to mui.o(i.mui_get_fds_char) for mui_get_fds_char
    mui.o(i.mui_fds_get_nth_token) refers to mui.o(i.mui_fds_first_token) for mui_fds_first_token
    mui.o(i.mui_fds_get_nth_token) refers to mui.o(i.mui_fds_next_token) for mui_fds_next_token
    mui.o(i.mui_fds_get_token_cnt) refers to mui.o(i.mui_fds_first_token) for mui_fds_first_token
    mui.o(i.mui_fds_get_token_cnt) refers to mui.o(i.mui_fds_next_token) for mui_fds_next_token
    mui.o(i.mui_fds_next_token) refers to mui.o(i.mui_get_fds_char) for mui_get_fds_char
    mui.o(i.mui_fds_parse_text) refers to mui.o(i.mui_get_fds_char) for mui_get_fds_char
    mui.o(i.mui_find_form) refers to mui.o(i.mui_get_fds_char) for mui_get_fds_char
    mui.o(i.mui_find_form) refers to mui.o(i.mui_fds_get_cmd_size) for mui_fds_get_cmd_size
    mui.o(i.mui_inner_loop_over_form) refers to mui.o(i.mui_fds_get_cmd_size) for mui_fds_get_cmd_size
    mui.o(i.mui_inner_loop_over_form) refers to mui.o(i.mui_get_fds_char) for mui_get_fds_char
    mui.o(i.mui_inner_loop_over_form) refers to mui.o(i.mui_prepare_current_field) for mui_prepare_current_field
    mui.o(i.mui_loop_over_form) refers to mui.o(i.mui_inner_loop_over_form) for mui_inner_loop_over_form
    mui.o(i.mui_next_field) refers to mui.o(i.mui_loop_over_form) for mui_loop_over_form
    mui.o(i.mui_next_field) refers to mui.o(i.mui_task_find_next_cursor_uif) for mui_task_find_next_cursor_uif
    mui.o(i.mui_next_field) refers to mui.o(i.mui_task_find_first_cursor_uif) for mui_task_find_first_cursor_uif
    mui.o(i.mui_prepare_current_field) refers to mui.o(i.mui_fds_get_cmd_size) for mui_fds_get_cmd_size
    mui.o(i.mui_prepare_current_field) refers to mui.o(i.mui_get_fds_char) for mui_get_fds_char
    mui.o(i.mui_prepare_current_field) refers to mui.o(i.mui_find_uif) for mui_find_uif
    mui.o(i.mui_send_cursor_enter_msg) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_send_cursor_msg) refers to mui.o(i.mui_prepare_current_field) for mui_prepare_current_field
    mui.o(i.mui_task_find_first_cursor_uif) refers to mui.o(i.mui_uif_is_cursor_selectable) for mui_uif_is_cursor_selectable
    mui.o(i.mui_task_find_last_cursor_uif) refers to mui.o(i.mui_uif_is_cursor_selectable) for mui_uif_is_cursor_selectable
    mui.o(i.mui_task_find_next_cursor_uif) refers to mui.o(i.mui_uif_is_cursor_selectable) for mui_uif_is_cursor_selectable
    mui.o(i.mui_task_find_prev_cursor_uif) refers to mui.o(i.mui_uif_is_cursor_selectable) for mui_uif_is_cursor_selectable
    mui.o(i.mui_task_get_current_cursor_focus_position) refers to mui.o(i.mui_uif_is_cursor_selectable) for mui_uif_is_cursor_selectable
    mui_u8g2.o(i.mui_get_x) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_hline) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_hline) refers to mui_u8g2.o(i.mui_get_y) for mui_get_y
    mui_u8g2.o(i.mui_hline) refers to u8g2_hvline.o(i.u8g2_DrawHLine) for u8g2_DrawHLine
    mui_u8g2.o(i.mui_u8g2_btn_back_w1_fi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_btn_back_w1_fi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_btn_back_w1_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_fi) for mui_u8g2_draw_button_fi
    mui_u8g2.o(i.mui_u8g2_btn_back_w1_fi) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_btn_back_w1_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_btn_back_w1_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_btn_back_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_btn_back_w1_pi) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_btn_back_w2_fi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_btn_back_w2_fi) refers to mui_u8g2.o(i.mui_u8g2_get_fi_flags) for mui_u8g2_get_fi_flags
    mui_u8g2.o(i.mui_u8g2_btn_back_w2_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_back_w2_fi) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_btn_back_w2_if) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_btn_back_w2_if) refers to mui_u8g2.o(i.mui_u8g2_get_if_flags) for mui_u8g2_get_if_flags
    mui_u8g2.o(i.mui_u8g2_btn_back_w2_if) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_back_w2_if) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_btn_back_wm_fi) refers to mui_u8g2.o(i.mui_u8g2_get_fi_flags) for mui_u8g2_get_fi_flags
    mui_u8g2.o(i.mui_u8g2_btn_back_wm_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_back_wm_fi) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_btn_back_wm_if) refers to mui_u8g2.o(i.mui_u8g2_get_if_flags) for mui_u8g2_get_if_flags
    mui_u8g2.o(i.mui_u8g2_btn_back_wm_if) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_back_wm_if) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_btn_exit_wm_fi) refers to mui_u8g2.o(i.mui_u8g2_get_fi_flags) for mui_u8g2_get_fi_flags
    mui_u8g2.o(i.mui_u8g2_btn_exit_wm_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_exit_wm_fi) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_btn_exit_wm_fi) refers to mui.o(i.mui_LeaveForm) for mui_LeaveForm
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_fi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_fi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_fi) for mui_u8g2_draw_button_fi
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_fi) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_fi) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_pi) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_pi) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_fi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_fi) refers to mui_u8g2.o(i.mui_u8g2_get_fi_flags) for mui_u8g2_get_fi_flags
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_fi) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_fi) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_if) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_if) refers to mui_u8g2.o(i.mui_u8g2_get_if_flags) for mui_u8g2_get_if_flags
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_if) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_if) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_if) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_fi) refers to mui_u8g2.o(i.mui_u8g2_get_fi_flags) for mui_u8g2_get_fi_flags
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_fi) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_fi) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_if) refers to mui_u8g2.o(i.mui_u8g2_get_if_flags) for mui_u8g2_get_if_flags
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_if) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_if) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_if) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_draw_button_fi) refers to mui_u8g2.o(i.mui_u8g2_get_fi_flags) for mui_u8g2_get_fi_flags
    mui_u8g2.o(i.mui_u8g2_draw_button_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_draw_button_if) refers to mui_u8g2.o(i.mui_u8g2_get_if_flags) for mui_u8g2_get_if_flags
    mui_u8g2.o(i.mui_u8g2_draw_button_if) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_draw_button_pf) refers to mui_u8g2.o(i.mui_u8g2_get_pf_flags) for mui_u8g2_get_pf_flags
    mui_u8g2.o(i.mui_u8g2_draw_button_pf) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_draw_button_pi) refers to mui_u8g2.o(i.mui_u8g2_get_pi_flags) for mui_u8g2_get_pi_flags
    mui_u8g2.o(i.mui_u8g2_draw_button_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_draw_button_utf) refers to mui_u8g2.o(i.mui_get_y) for mui_get_y
    mui_u8g2.o(i.mui_u8g2_draw_button_utf) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_draw_button_utf) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_draw_button_utf) refers to u8g2_button.o(i.u8g2_DrawButtonUTF8) for u8g2_DrawButtonUTF8
    mui_u8g2.o(i.mui_u8g2_draw_text) refers to mui_u8g2.o(i.mui_get_y) for mui_get_y
    mui_u8g2.o(i.mui_u8g2_draw_text) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_draw_text) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_draw_text) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui.o(i.mui_GetSelectableFieldTextOption) for mui_GetSelectableFieldTextOption
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pf) for mui_u8g2_draw_button_pf
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui.o(i.mui_SaveFormWithCursorPosition) for mui_SaveFormWithCursorPosition
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui.o(i.mui_SaveCursorPosition) for mui_SaveCursorPosition
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) for mui_u8g2_u8_opt_child_mse_common
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui.o(i.mui_GetSelectableFieldTextOption) for mui_GetSelectableFieldTextOption
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui.o(i.mui_SaveFormWithCursorPosition) for mui_SaveFormWithCursorPosition
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui.o(i.mui_SaveCursorPosition) for mui_SaveCursorPosition
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) for mui_u8g2_u8_opt_child_mse_common
    mui_u8g2.o(i.mui_u8g2_s8_min_max_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pf) for mui_u8g2_s8_vmm_draw_wm_pf
    mui_u8g2.o(i.mui_u8g2_s8_min_max_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pi) for mui_u8g2_s8_vmm_draw_wm_pi
    mui_u8g2.o(i.mui_u8g2_s8_min_max_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pf) for mui_u8g2_s8_vmm_draw_wm_pf
    mui_u8g2.o(i.mui_u8g2_s8_min_max_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pi) for mui_u8g2_s8_vmm_draw_wm_pi
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pf) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pf) refers to u8x8_u8toa.o(i.u8x8_s8toa) for u8x8_s8toa
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pf) refers to u8g2_font.o(i.u8g2_GetStrWidth) for u8g2_GetStrWidth
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pf) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pf) for mui_u8g2_draw_button_pf
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pi) refers to u8x8_u8toa.o(i.u8x8_s8toa) for u8x8_s8toa
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pi) refers to u8g2_font.o(i.u8g2_GetStrWidth) for u8g2_GetStrWidth
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pi) refers to mui_u8g2.o(.constdata) for .constdata
    mui_u8g2.o(i.mui_u8g2_set_font_style_function) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_set_font_style_function) refers to u8g2_font.o(i.u8g2_SetFont) for u8g2_SetFont
    mui_u8g2.o(i.mui_u8g2_u16_list_child_mse_common) refers to mui_u8g2.o(i.mui_u8g2_handle_scroll_next_prev_events) for mui_u8g2_handle_scroll_next_prev_events
    mui_u8g2.o(i.mui_u8g2_u16_list_child_mse_common) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_u16_list_child_w1_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_u16_list_child_w1_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u16_list_child_w1_pi) refers to mui_u8g2.o(i.mui_get_y) for mui_get_y
    mui_u8g2.o(i.mui_u8g2_u16_list_child_w1_pi) refers to mui_u8g2.o(i.u8g2_DrawValueMark) for u8g2_DrawValueMark
    mui_u8g2.o(i.mui_u8g2_u16_list_child_w1_pi) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    mui_u8g2.o(i.mui_u8g2_u16_list_child_w1_pi) refers to u8g2_button.o(i.u8g2_DrawButtonFrame) for u8g2_DrawButtonFrame
    mui_u8g2.o(i.mui_u8g2_u16_list_child_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_u16_list_child_mse_common) for mui_u8g2_u16_list_child_mse_common
    mui_u8g2.o(i.mui_u8g2_u16_list_goto_w1_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_u16_list_goto_w1_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u16_list_goto_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u16_list_goto_w1_pi) refers to mui.o(i.mui_SaveFormWithCursorPosition) for mui_SaveFormWithCursorPosition
    mui_u8g2.o(i.mui_u8g2_u16_list_goto_w1_pi) refers to mui.o(i.mui_SaveCursorPosition) for mui_SaveCursorPosition
    mui_u8g2.o(i.mui_u8g2_u16_list_goto_w1_pi) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_u16_list_goto_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_u16_list_child_mse_common) for mui_u8g2_u16_list_child_mse_common
    mui_u8g2.o(i.mui_u8g2_u16_list_line_wa_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u16_list_line_wa_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u16_list_parent_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u16_list_parent_wm_pi) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_u16_list_parent_wm_pi) refers to mui.o(i.mui_GotoForm) for mui_GotoForm
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to mui_u8g2.o(i.mui_get_y) for mui_get_y
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to u8g2_box.o(i.u8g2_DrawFrame) for u8g2_DrawFrame
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to u8g2_box.o(i.u8g2_DrawBox) for u8g2_DrawBox
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to u8x8_u8toa.o(i.u8x8_u8toa) for u8x8_u8toa
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to u8g2_font.o(i.u8g2_DrawStr) for u8g2_DrawStr
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to u8g2_font.o(i.u8g2_GetStrWidth) for u8g2_GetStrWidth
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to u8g2_button.o(i.u8g2_DrawButtonFrame) for u8g2_DrawButtonFrame
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_get_pf_flags) for mui_u8g2_get_pf_flags
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mse_msg_handler) for mui_u8g2_u8_bar_mse_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_get_pi_flags) for mui_u8g2_get_pi_flags
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mse_msg_handler) for mui_u8g2_u8_bar_mse_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_get_pf_flags) for mui_u8g2_get_pf_flags
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mud_msg_handler) for mui_u8g2_u8_bar_mud_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_get_pi_flags) for mui_u8g2_get_pi_flags
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mud_msg_handler) for mui_u8g2_u8_bar_mud_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_char_wm_mud_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_u8_char_wm_mud_pi) refers to mui_u8g2.o(i.mui_is_valid_char) for mui_is_valid_char
    mui_u8g2.o(i.mui_u8g2_u8_char_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u8_chkbox_wm_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_u8_chkbox_wm_pi) refers to mui_u8g2.o(i.mui_get_y) for mui_get_y
    mui_u8g2.o(i.mui_u8g2_u8_chkbox_wm_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u8_chkbox_wm_pi) refers to mui_u8g2.o(i.u8g2_DrawCheckbox) for u8g2_DrawCheckbox
    mui_u8g2.o(i.mui_u8g2_u8_chkbox_wm_pi) refers to u8g2_font.o(i.u8g2_GetUTF8Width) for u8g2_GetUTF8Width
    mui_u8g2.o(i.mui_u8g2_u8_chkbox_wm_pi) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    mui_u8g2.o(i.mui_u8g2_u8_chkbox_wm_pi) refers to u8g2_button.o(i.u8g2_DrawButtonFrame) for u8g2_DrawButtonFrame
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_get_pf_flags) for mui_u8g2_get_pf_flags
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mse_msg_handler) for mui_u8g2_u8_bar_mse_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_get_pi_flags) for mui_u8g2_get_pi_flags
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mse_msg_handler) for mui_u8g2_u8_bar_mse_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_get_pf_flags) for mui_u8g2_get_pf_flags
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mud_msg_handler) for mui_u8g2_u8_bar_mud_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_get_pi_flags) for mui_u8g2_get_pi_flags
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mud_msg_handler) for mui_u8g2_u8_bar_mud_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_min_max_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pf) for mui_u8g2_u8_vmm_draw_wm_pf
    mui_u8g2.o(i.mui_u8g2_u8_min_max_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pi) for mui_u8g2_u8_vmm_draw_wm_pi
    mui_u8g2.o(i.mui_u8g2_u8_min_max_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pf) for mui_u8g2_u8_vmm_draw_wm_pf
    mui_u8g2.o(i.mui_u8g2_u8_min_max_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pi) for mui_u8g2_u8_vmm_draw_wm_pi
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) refers to mui.o(i.mui_GetSelectableFieldOptionCnt) for mui_GetSelectableFieldOptionCnt
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) refers to mui_u8g2.o(i.mui_u8g2_handle_scroll_next_prev_events) for mui_u8g2_handle_scroll_next_prev_events
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_wm_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_wm_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_wm_pi) refers to mui_u8g2.o(i.mui_get_y) for mui_get_y
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_get_pi_flags) for mui_u8g2_get_pi_flags
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_wm_pi) refers to mui.o(i.mui_GetSelectableFieldTextOption) for mui_GetSelectableFieldTextOption
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_wm_pi) refers to u8g2_button.o(i.u8g2_DrawButtonUTF8) for u8g2_DrawButtonUTF8
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) for mui_u8g2_u8_opt_child_mse_common
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mse_pf) refers to mui.o(i.mui_fds_get_nth_token) for mui_fds_get_nth_token
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pf) for mui_u8g2_draw_button_pf
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mse_pf) refers to mui.o(i.mui_fds_get_token_cnt) for mui_fds_get_token_cnt
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mse_pi) refers to mui.o(i.mui_fds_get_nth_token) for mui_fds_get_nth_token
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mse_pi) refers to mui.o(i.mui_fds_get_token_cnt) for mui_fds_get_token_cnt
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mud_pf) refers to mui.o(i.mui_fds_get_nth_token) for mui_fds_get_nth_token
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pf) for mui_u8g2_draw_button_pf
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mud_pf) refers to mui.o(i.mui_fds_get_token_cnt) for mui_fds_get_token_cnt
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mud_pi) refers to mui.o(i.mui_fds_get_nth_token) for mui_fds_get_nth_token
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mud_pi) refers to mui.o(i.mui_fds_get_token_cnt) for mui_fds_get_token_cnt
    mui_u8g2.o(i.mui_u8g2_u8_opt_parent_wm_pi) refers to mui.o(i.mui_fds_get_nth_token) for mui_fds_get_nth_token
    mui_u8g2.o(i.mui_u8g2_u8_opt_parent_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u8_opt_parent_wm_pi) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_u8_opt_parent_wm_pi) refers to mui.o(i.mui_GotoForm) for mui_GotoForm
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi) refers to mui_u8g2.o(i.mui_get_y) for mui_get_y
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi) refers to mui_u8g2.o(i.u8g2_DrawValueMark) for u8g2_DrawValueMark
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi) refers to mui.o(i.mui_GetSelectableFieldTextOption) for mui_GetSelectableFieldTextOption
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi) refers to u8g2_button.o(i.u8g2_DrawButtonFrame) for u8g2_DrawButtonFrame
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) for mui_u8g2_u8_opt_child_mse_common
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to mui_u8g2.o(i.mui_get_y) for mui_get_y
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to mui_u8g2.o(i.u8g2_DrawValueMark) for u8g2_DrawValueMark
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to mui.o(i.mui_GetSelectableFieldTextOption) for mui_GetSelectableFieldTextOption
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to u8g2_font.o(i.u8g2_GetUTF8Width) for u8g2_GetUTF8Width
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to u8g2_button.o(i.u8g2_DrawButtonFrame) for u8g2_DrawButtonFrame
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) for mui_u8g2_u8_opt_child_mse_common
    mui_u8g2.o(i.mui_u8g2_u8_radio_wm_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_u8_radio_wm_pi) refers to mui_u8g2.o(i.mui_get_y) for mui_get_y
    mui_u8g2.o(i.mui_u8g2_u8_radio_wm_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u8_radio_wm_pi) refers to mui_u8g2.o(i.u8g2_DrawCheckbox) for u8g2_DrawCheckbox
    mui_u8g2.o(i.mui_u8g2_u8_radio_wm_pi) refers to u8g2_font.o(i.u8g2_GetUTF8Width) for u8g2_GetUTF8Width
    mui_u8g2.o(i.mui_u8g2_u8_radio_wm_pi) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    mui_u8g2.o(i.mui_u8g2_u8_radio_wm_pi) refers to u8g2_button.o(i.u8g2_DrawButtonFrame) for u8g2_DrawButtonFrame
    mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pf) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pf) refers to u8x8_u8toa.o(i.u8x8_u8toa) for u8x8_u8toa
    mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pf) refers to u8g2_font.o(i.u8g2_GetStrWidth) for u8g2_GetStrWidth
    mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pf) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pf) for mui_u8g2_draw_button_pf
    mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pi) refers to u8x8_u8toa.o(i.u8x8_u8toa) for u8x8_u8toa
    mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pi) refers to u8g2_font.o(i.u8g2_GetStrWidth) for u8g2_GetStrWidth
    mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_x8_min_max_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pf) for mui_u8g2_x8_vmm_draw_wm_pf
    mui_u8g2.o(i.mui_u8g2_x8_min_max_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pf) for mui_u8g2_x8_vmm_draw_wm_pf
    mui_u8g2.o(i.mui_u8g2_x8_min_max_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pi) for mui_u8g2_x8_vmm_draw_wm_pi
    mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pf) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pf) refers to u8x8_u8toa.o(i.u8x8_u8tox) for u8x8_u8tox
    mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pf) refers to u8g2_font.o(i.u8g2_GetStrWidth) for u8g2_GetStrWidth
    mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pf) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pf) for mui_u8g2_draw_button_pf
    mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pi) refers to u8x8_u8toa.o(i.u8x8_u8tox) for u8x8_u8tox
    mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pi) refers to u8g2_font.o(i.u8g2_GetStrWidth) for u8g2_GetStrWidth
    mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_x8g2_x8_min_max_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pi) for mui_u8g2_x8_vmm_draw_wm_pi
    mui_u8g2.o(i.u8g2_DrawCheckbox) refers to u8g2_box.o(i.u8g2_DrawFrame) for u8g2_DrawFrame
    mui_u8g2.o(i.u8g2_DrawCheckbox) refers to u8g2_box.o(i.u8g2_DrawBox) for u8g2_DrawBox
    mui_u8g2.o(i.u8g2_DrawValueMark) refers to u8g2_box.o(i.u8g2_DrawBox) for u8g2_DrawBox
    u8g2_arc.o(i.u8g2_DrawArc) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_arc.o(i.u8g2_DrawArc) refers to u8g2_arc.o(i.u8g2_draw_arc) for u8g2_draw_arc
    u8g2_arc.o(i.u8g2_draw_arc) refers to u8g2_hvline.o(i.u8g2_DrawPixel) for u8g2_DrawPixel
    u8g2_bitmap.o(i.u8g2_DrawBitmap) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_bitmap.o(i.u8g2_DrawBitmap) refers to u8g2_bitmap.o(i.u8g2_DrawHorizontalBitmap) for u8g2_DrawHorizontalBitmap
    u8g2_bitmap.o(i.u8g2_DrawHXBM) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_bitmap.o(i.u8g2_DrawHXBM) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_bitmap.o(i.u8g2_DrawHXBMP) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_bitmap.o(i.u8g2_DrawHXBMP) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_bitmap.o(i.u8g2_DrawHorizontalBitmap) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_bitmap.o(i.u8g2_DrawHorizontalBitmap) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_bitmap.o(i.u8g2_DrawXBM) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_bitmap.o(i.u8g2_DrawXBM) refers to u8g2_bitmap.o(i.u8g2_DrawHXBM) for u8g2_DrawHXBM
    u8g2_bitmap.o(i.u8g2_DrawXBMP) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_bitmap.o(i.u8g2_DrawXBMP) refers to u8g2_bitmap.o(i.u8g2_DrawHXBMP) for u8g2_DrawHXBMP
    u8g2_box.o(i.u8g2_DrawBox) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_box.o(i.u8g2_DrawBox) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_box.o(i.u8g2_DrawFrame) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_box.o(i.u8g2_DrawFrame) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_box.o(i.u8g2_DrawRBox) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_box.o(i.u8g2_DrawRBox) refers to u8g2_circle.o(i.u8g2_DrawDisc) for u8g2_DrawDisc
    u8g2_box.o(i.u8g2_DrawRBox) refers to u8g2_box.o(i.u8g2_DrawBox) for u8g2_DrawBox
    u8g2_box.o(i.u8g2_DrawRFrame) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_box.o(i.u8g2_DrawRFrame) refers to u8g2_circle.o(i.u8g2_DrawCircle) for u8g2_DrawCircle
    u8g2_box.o(i.u8g2_DrawRFrame) refers to u8g2_hvline.o(i.u8g2_DrawHLine) for u8g2_DrawHLine
    u8g2_box.o(i.u8g2_DrawRFrame) refers to u8g2_hvline.o(i.u8g2_DrawVLine) for u8g2_DrawVLine
    u8g2_buffer.o(i.u8g2_ClearBuffer) refers to memseta.o(.text) for __aeabi_memclr
    u8g2_buffer.o(i.u8g2_FirstPage) refers to u8g2_buffer.o(i.u8g2_ClearBuffer) for u8g2_ClearBuffer
    u8g2_buffer.o(i.u8g2_FirstPage) refers to u8g2_buffer.o(i.u8g2_SetBufferCurrTileRow) for u8g2_SetBufferCurrTileRow
    u8g2_buffer.o(i.u8g2_NextPage) refers to u8g2_buffer.o(i.u8g2_send_buffer) for u8g2_send_buffer
    u8g2_buffer.o(i.u8g2_NextPage) refers to u8x8_display.o(i.u8x8_RefreshDisplay) for u8x8_RefreshDisplay
    u8g2_buffer.o(i.u8g2_NextPage) refers to u8g2_buffer.o(i.u8g2_ClearBuffer) for u8g2_ClearBuffer
    u8g2_buffer.o(i.u8g2_NextPage) refers to u8g2_buffer.o(i.u8g2_SetBufferCurrTileRow) for u8g2_SetBufferCurrTileRow
    u8g2_buffer.o(i.u8g2_SendBuffer) refers to u8g2_buffer.o(i.u8g2_send_buffer) for u8g2_send_buffer
    u8g2_buffer.o(i.u8g2_SendBuffer) refers to u8x8_display.o(i.u8x8_RefreshDisplay) for u8x8_RefreshDisplay
    u8g2_buffer.o(i.u8g2_UpdateDisplay) refers to u8g2_buffer.o(i.u8g2_send_buffer) for u8g2_send_buffer
    u8g2_buffer.o(i.u8g2_UpdateDisplayArea) refers to u8x8_display.o(i.u8x8_DrawTile) for u8x8_DrawTile
    u8g2_buffer.o(i.u8g2_WriteBufferPBM) refers to u8x8_capture.o(i.u8x8_capture_write_pbm_pre) for u8x8_capture_write_pbm_pre
    u8g2_buffer.o(i.u8g2_WriteBufferPBM) refers to u8x8_capture.o(i.u8x8_capture_write_pbm_buffer) for u8x8_capture_write_pbm_buffer
    u8g2_buffer.o(i.u8g2_WriteBufferPBM) refers to u8x8_capture.o(i.u8x8_capture_get_pixel_1) for u8x8_capture_get_pixel_1
    u8g2_buffer.o(i.u8g2_WriteBufferPBM2) refers to u8x8_capture.o(i.u8x8_capture_write_pbm_pre) for u8x8_capture_write_pbm_pre
    u8g2_buffer.o(i.u8g2_WriteBufferPBM2) refers to u8x8_capture.o(i.u8x8_capture_write_pbm_buffer) for u8x8_capture_write_pbm_buffer
    u8g2_buffer.o(i.u8g2_WriteBufferPBM2) refers to u8x8_capture.o(i.u8x8_capture_get_pixel_2) for u8x8_capture_get_pixel_2
    u8g2_buffer.o(i.u8g2_WriteBufferXBM) refers to u8x8_capture.o(i.u8x8_capture_write_xbm_pre) for u8x8_capture_write_xbm_pre
    u8g2_buffer.o(i.u8g2_WriteBufferXBM) refers to u8x8_capture.o(i.u8x8_capture_write_xbm_buffer) for u8x8_capture_write_xbm_buffer
    u8g2_buffer.o(i.u8g2_WriteBufferXBM) refers to u8x8_capture.o(i.u8x8_capture_get_pixel_1) for u8x8_capture_get_pixel_1
    u8g2_buffer.o(i.u8g2_WriteBufferXBM2) refers to u8x8_capture.o(i.u8x8_capture_write_xbm_pre) for u8x8_capture_write_xbm_pre
    u8g2_buffer.o(i.u8g2_WriteBufferXBM2) refers to u8x8_capture.o(i.u8x8_capture_write_xbm_buffer) for u8x8_capture_write_xbm_buffer
    u8g2_buffer.o(i.u8g2_WriteBufferXBM2) refers to u8x8_capture.o(i.u8x8_capture_get_pixel_2) for u8x8_capture_get_pixel_2
    u8g2_buffer.o(i.u8g2_send_buffer) refers to u8g2_buffer.o(i.u8g2_send_tile_row) for u8g2_send_tile_row
    u8g2_buffer.o(i.u8g2_send_tile_row) refers to u8x8_display.o(i.u8x8_DrawTile) for u8x8_DrawTile
    u8g2_button.o(i.u8g2_DrawButtonFrame) refers to u8g2_hvline.o(i.u8g2_SetDrawColor) for u8g2_SetDrawColor
    u8g2_button.o(i.u8g2_DrawButtonFrame) refers to u8g2_box.o(i.u8g2_DrawFrame) for u8g2_DrawFrame
    u8g2_button.o(i.u8g2_DrawButtonFrame) refers to u8g2_hvline.o(i.u8g2_DrawHLine) for u8g2_DrawHLine
    u8g2_button.o(i.u8g2_DrawButtonFrame) refers to u8g2_hvline.o(i.u8g2_DrawVLine) for u8g2_DrawVLine
    u8g2_button.o(i.u8g2_DrawButtonFrame) refers to u8g2_box.o(i.u8g2_DrawBox) for u8g2_DrawBox
    u8g2_button.o(i.u8g2_DrawButtonUTF8) refers to u8g2_font.o(i.u8g2_GetUTF8Width) for u8g2_GetUTF8Width
    u8g2_button.o(i.u8g2_DrawButtonUTF8) refers to u8g2_font.o(i.u8g2_SetFontMode) for u8g2_SetFontMode
    u8g2_button.o(i.u8g2_DrawButtonUTF8) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    u8g2_button.o(i.u8g2_DrawButtonUTF8) refers to u8g2_button.o(i.u8g2_DrawButtonFrame) for u8g2_DrawButtonFrame
    u8g2_circle.o(i.u8g2_DrawCircle) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_circle.o(i.u8g2_DrawCircle) refers to u8g2_circle.o(i.u8g2_draw_circle) for u8g2_draw_circle
    u8g2_circle.o(i.u8g2_DrawDisc) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_circle.o(i.u8g2_DrawDisc) refers to u8g2_circle.o(i.u8g2_draw_disc) for u8g2_draw_disc
    u8g2_circle.o(i.u8g2_DrawEllipse) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_circle.o(i.u8g2_DrawEllipse) refers to u8g2_circle.o(i.u8g2_draw_ellipse) for u8g2_draw_ellipse
    u8g2_circle.o(i.u8g2_DrawFilledEllipse) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_circle.o(i.u8g2_DrawFilledEllipse) refers to u8g2_circle.o(i.u8g2_draw_filled_ellipse) for u8g2_draw_filled_ellipse
    u8g2_circle.o(i.u8g2_draw_circle) refers to u8g2_circle.o(i.u8g2_draw_circle_section) for u8g2_draw_circle_section
    u8g2_circle.o(i.u8g2_draw_circle_section) refers to u8g2_hvline.o(i.u8g2_DrawPixel) for u8g2_DrawPixel
    u8g2_circle.o(i.u8g2_draw_disc) refers to u8g2_circle.o(i.u8g2_draw_disc_section) for u8g2_draw_disc_section
    u8g2_circle.o(i.u8g2_draw_disc_section) refers to u8g2_hvline.o(i.u8g2_DrawVLine) for u8g2_DrawVLine
    u8g2_circle.o(i.u8g2_draw_ellipse) refers to u8g2_circle.o(i.u8g2_draw_ellipse_section) for u8g2_draw_ellipse_section
    u8g2_circle.o(i.u8g2_draw_ellipse_section) refers to u8g2_hvline.o(i.u8g2_DrawPixel) for u8g2_DrawPixel
    u8g2_circle.o(i.u8g2_draw_filled_ellipse) refers to u8g2_circle.o(i.u8g2_draw_filled_ellipse_section) for u8g2_draw_filled_ellipse_section
    u8g2_circle.o(i.u8g2_draw_filled_ellipse_section) refers to u8g2_hvline.o(i.u8g2_DrawVLine) for u8g2_DrawVLine
    u8g2_cleardisplay.o(i.u8g2_ClearDisplay) refers to u8g2_buffer.o(i.u8g2_FirstPage) for u8g2_FirstPage
    u8g2_cleardisplay.o(i.u8g2_ClearDisplay) refers to u8g2_buffer.o(i.u8g2_NextPage) for u8g2_NextPage
    u8g2_cleardisplay.o(i.u8g2_ClearDisplay) refers to u8g2_buffer.o(i.u8g2_SetBufferCurrTileRow) for u8g2_SetBufferCurrTileRow
    u8g2_d_memory.o(i.u8g2_m_16_8_f) refers to u8g2_d_memory.o(.bss) for buf
    u8g2_d_setup.o(i.u8g2_Setup_ssd1315_i2c_128x64_noname_f) refers to u8x8_setup.o(i.u8x8_Setup) for u8x8_Setup
    u8g2_d_setup.o(i.u8g2_Setup_ssd1315_i2c_128x64_noname_f) refers to u8g2_d_memory.o(i.u8g2_m_16_8_f) for u8g2_m_16_8_f
    u8g2_d_setup.o(i.u8g2_Setup_ssd1315_i2c_128x64_noname_f) refers to u8g2_setup.o(i.u8g2_SetupBuffer) for u8g2_SetupBuffer
    u8g2_d_setup.o(i.u8g2_Setup_ssd1315_i2c_128x64_noname_f) refers to u8x8_cad.o(i.u8x8_cad_ssd13xx_fast_i2c) for u8x8_cad_ssd13xx_fast_i2c
    u8g2_d_setup.o(i.u8g2_Setup_ssd1315_i2c_128x64_noname_f) refers to u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_128x64_noname) for u8x8_d_ssd1315_128x64_noname
    u8g2_d_setup.o(i.u8g2_Setup_ssd1315_i2c_128x64_noname_f) refers to u8g2_ll_hvline.o(i.u8g2_ll_hvline_vertical_top_lsb) for u8g2_ll_hvline_vertical_top_lsb
    u8g2_font.o(i.u8g2_DrawExtUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8g2_font.o(i.u8g2_DrawExtUTF8) refers to u8g2_font.o(i.u8g2_GetGlyphWidth) for u8g2_GetGlyphWidth
    u8g2_font.o(i.u8g2_DrawExtUTF8) refers to u8g2_kerning.o(i.u8g2_GetKerningByTable) for u8g2_GetKerningByTable
    u8g2_font.o(i.u8g2_DrawExtUTF8) refers to u8g2_font.o(i.u8g2_DrawGlyph) for u8g2_DrawGlyph
    u8g2_font.o(i.u8g2_DrawExtUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8g2_font.o(i.u8g2_DrawExtendedUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8g2_font.o(i.u8g2_DrawExtendedUTF8) refers to u8g2_font.o(i.u8g2_GetGlyphWidth) for u8g2_GetGlyphWidth
    u8g2_font.o(i.u8g2_DrawExtendedUTF8) refers to u8g2_kerning.o(i.u8g2_GetKerning) for u8g2_GetKerning
    u8g2_font.o(i.u8g2_DrawExtendedUTF8) refers to u8g2_font.o(i.u8g2_DrawGlyph) for u8g2_DrawGlyph
    u8g2_font.o(i.u8g2_DrawExtendedUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8g2_font.o(i.u8g2_DrawGlyph) refers to u8g2_font.o(i.u8g2_font_draw_glyph) for u8g2_font_draw_glyph
    u8g2_font.o(i.u8g2_DrawGlyphX2) refers to u8g2_font.o(i.u8g2_font_2x_draw_glyph) for u8g2_font_2x_draw_glyph
    u8g2_font.o(i.u8g2_DrawHB) refers to u8g2_font.o(i.u8g2_DrawGlyph) for u8g2_DrawGlyph
    u8g2_font.o(i.u8g2_DrawStr) refers to u8g2_font.o(i.u8g2_draw_string) for u8g2_draw_string
    u8g2_font.o(i.u8g2_DrawStr) refers to u8x8_8x8.o(i.u8x8_ascii_next) for u8x8_ascii_next
    u8g2_font.o(i.u8g2_DrawStrX2) refers to u8g2_font.o(i.u8g2_draw_string_2x) for u8g2_draw_string_2x
    u8g2_font.o(i.u8g2_DrawStrX2) refers to u8x8_8x8.o(i.u8x8_ascii_next) for u8x8_ascii_next
    u8g2_font.o(i.u8g2_DrawUTF8) refers to u8g2_font.o(i.u8g2_draw_string) for u8g2_draw_string
    u8g2_font.o(i.u8g2_DrawUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8g2_font.o(i.u8g2_DrawUTF8X2) refers to u8g2_font.o(i.u8g2_draw_string_2x) for u8g2_draw_string_2x
    u8g2_font.o(i.u8g2_DrawUTF8X2) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8g2_font.o(i.u8g2_GetFontSize) refers to u8g2_font.o(i.u8g2_font_get_word) for u8g2_font_get_word
    u8g2_font.o(i.u8g2_GetGlyphHorizontalProperties) refers to u8g2_font.o(i.u8g2_font_get_glyph_data) for u8g2_font_get_glyph_data
    u8g2_font.o(i.u8g2_GetGlyphHorizontalProperties) refers to u8g2_font.o(i.u8g2_font_setup_decode) for u8g2_font_setup_decode
    u8g2_font.o(i.u8g2_GetGlyphHorizontalProperties) refers to u8g2_font.o(i.u8g2_font_decode_get_signed_bits) for u8g2_font_decode_get_signed_bits
    u8g2_font.o(i.u8g2_GetGlyphWidth) refers to u8g2_font.o(i.u8g2_font_get_glyph_data) for u8g2_font_get_glyph_data
    u8g2_font.o(i.u8g2_GetGlyphWidth) refers to u8g2_font.o(i.u8g2_font_setup_decode) for u8g2_font_setup_decode
    u8g2_font.o(i.u8g2_GetGlyphWidth) refers to u8g2_font.o(i.u8g2_font_decode_get_signed_bits) for u8g2_font_decode_get_signed_bits
    u8g2_font.o(i.u8g2_GetStrWidth) refers to u8g2_font.o(i.u8g2_string_width) for u8g2_string_width
    u8g2_font.o(i.u8g2_GetStrWidth) refers to u8x8_8x8.o(i.u8x8_ascii_next) for u8x8_ascii_next
    u8g2_font.o(i.u8g2_GetStrX) refers to u8g2_font.o(i.u8g2_GetGlyphHorizontalProperties) for u8g2_GetGlyphHorizontalProperties
    u8g2_font.o(i.u8g2_GetUTF8Width) refers to u8g2_font.o(i.u8g2_string_width) for u8g2_string_width
    u8g2_font.o(i.u8g2_GetUTF8Width) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8g2_font.o(i.u8g2_GetXOffsetGlyph) refers to u8g2_font.o(i.u8g2_GetGlyphWidth) for u8g2_GetGlyphWidth
    u8g2_font.o(i.u8g2_GetXOffsetUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8g2_font.o(i.u8g2_GetXOffsetUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8g2_font.o(i.u8g2_GetXOffsetUTF8) refers to u8g2_font.o(i.u8g2_GetXOffsetGlyph) for u8g2_GetXOffsetGlyph
    u8g2_font.o(i.u8g2_IsAllValidUTF8) refers to u8g2_font.o(i.u8g2_is_all_valid) for u8g2_is_all_valid
    u8g2_font.o(i.u8g2_IsAllValidUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8g2_font.o(i.u8g2_IsGlyph) refers to u8g2_font.o(i.u8g2_font_get_glyph_data) for u8g2_font_get_glyph_data
    u8g2_font.o(i.u8g2_SetFont) refers to u8g2_font.o(i.u8g2_read_font_info) for u8g2_read_font_info
    u8g2_font.o(i.u8g2_SetFont) refers to u8g2_font.o(i.u8g2_UpdateRefHeight) for u8g2_UpdateRefHeight
    u8g2_font.o(i.u8g2_SetFontPosBaseline) refers to u8g2_font.o(i.u8g2_font_calc_vref_font) for u8g2_font_calc_vref_font
    u8g2_font.o(i.u8g2_SetFontPosBottom) refers to u8g2_font.o(i.u8g2_font_calc_vref_bottom) for u8g2_font_calc_vref_bottom
    u8g2_font.o(i.u8g2_SetFontPosCenter) refers to u8g2_font.o(i.u8g2_font_calc_vref_center) for u8g2_font_calc_vref_center
    u8g2_font.o(i.u8g2_SetFontPosTop) refers to u8g2_font.o(i.u8g2_font_calc_vref_top) for u8g2_font_calc_vref_top
    u8g2_font.o(i.u8g2_SetFontRefHeightAll) refers to u8g2_font.o(i.u8g2_UpdateRefHeight) for u8g2_UpdateRefHeight
    u8g2_font.o(i.u8g2_SetFontRefHeightExtendedText) refers to u8g2_font.o(i.u8g2_UpdateRefHeight) for u8g2_UpdateRefHeight
    u8g2_font.o(i.u8g2_SetFontRefHeightText) refers to u8g2_font.o(i.u8g2_UpdateRefHeight) for u8g2_UpdateRefHeight
    u8g2_font.o(i.u8g2_draw_string) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8g2_font.o(i.u8g2_draw_string) refers to u8g2_font.o(i.u8g2_DrawGlyph) for u8g2_DrawGlyph
    u8g2_font.o(i.u8g2_draw_string_2x) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8g2_font.o(i.u8g2_draw_string_2x) refers to u8g2_font.o(i.u8g2_DrawGlyphX2) for u8g2_DrawGlyphX2
    u8g2_font.o(i.u8g2_font_2x_decode_glyph) refers to u8g2_font.o(i.u8g2_font_setup_decode) for u8g2_font_setup_decode
    u8g2_font.o(i.u8g2_font_2x_decode_glyph) refers to u8g2_font.o(i.u8g2_font_decode_get_signed_bits) for u8g2_font_decode_get_signed_bits
    u8g2_font.o(i.u8g2_font_2x_decode_glyph) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_font.o(i.u8g2_font_2x_decode_glyph) refers to u8g2_font.o(i.u8g2_font_decode_get_unsigned_bits) for u8g2_font_decode_get_unsigned_bits
    u8g2_font.o(i.u8g2_font_2x_decode_glyph) refers to u8g2_font.o(i.u8g2_font_2x_decode_len) for u8g2_font_2x_decode_len
    u8g2_font.o(i.u8g2_font_2x_decode_len) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_font.o(i.u8g2_font_2x_draw_glyph) refers to u8g2_font.o(i.u8g2_font_get_glyph_data) for u8g2_font_get_glyph_data
    u8g2_font.o(i.u8g2_font_2x_draw_glyph) refers to u8g2_font.o(i.u8g2_font_2x_decode_glyph) for u8g2_font_2x_decode_glyph
    u8g2_font.o(i.u8g2_font_decode_get_signed_bits) refers to u8g2_font.o(i.u8g2_font_decode_get_unsigned_bits) for u8g2_font_decode_get_unsigned_bits
    u8g2_font.o(i.u8g2_font_decode_glyph) refers to u8g2_font.o(i.u8g2_font_setup_decode) for u8g2_font_setup_decode
    u8g2_font.o(i.u8g2_font_decode_glyph) refers to u8g2_font.o(i.u8g2_font_decode_get_signed_bits) for u8g2_font_decode_get_signed_bits
    u8g2_font.o(i.u8g2_font_decode_glyph) refers to u8g2_font.o(i.u8g2_add_vector_x) for u8g2_add_vector_x
    u8g2_font.o(i.u8g2_font_decode_glyph) refers to u8g2_font.o(i.u8g2_add_vector_y) for u8g2_add_vector_y
    u8g2_font.o(i.u8g2_font_decode_glyph) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_font.o(i.u8g2_font_decode_glyph) refers to u8g2_font.o(i.u8g2_font_decode_get_unsigned_bits) for u8g2_font_decode_get_unsigned_bits
    u8g2_font.o(i.u8g2_font_decode_glyph) refers to u8g2_font.o(i.u8g2_font_decode_len) for u8g2_font_decode_len
    u8g2_font.o(i.u8g2_font_decode_len) refers to u8g2_font.o(i.u8g2_add_vector_x) for u8g2_add_vector_x
    u8g2_font.o(i.u8g2_font_decode_len) refers to u8g2_font.o(i.u8g2_add_vector_y) for u8g2_add_vector_y
    u8g2_font.o(i.u8g2_font_decode_len) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_font.o(i.u8g2_font_draw_glyph) refers to u8g2_font.o(i.u8g2_font_get_glyph_data) for u8g2_font_get_glyph_data
    u8g2_font.o(i.u8g2_font_draw_glyph) refers to u8g2_font.o(i.u8g2_font_decode_glyph) for u8g2_font_decode_glyph
    u8g2_font.o(i.u8g2_font_get_glyph_data) refers to u8g2_font.o(i.u8g2_font_get_word) for u8g2_font_get_word
    u8g2_font.o(i.u8g2_font_setup_decode) refers to u8g2_font.o(i.u8g2_font_decode_get_unsigned_bits) for u8g2_font_decode_get_unsigned_bits
    u8g2_font.o(i.u8g2_is_all_valid) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8g2_font.o(i.u8g2_is_all_valid) refers to u8g2_font.o(i.u8g2_font_get_glyph_data) for u8g2_font_get_glyph_data
    u8g2_font.o(i.u8g2_read_font_info) refers to u8g2_font.o(i.u8g2_font_get_byte) for u8g2_font_get_byte
    u8g2_font.o(i.u8g2_read_font_info) refers to u8g2_font.o(i.u8g2_font_get_word) for u8g2_font_get_word
    u8g2_font.o(i.u8g2_string_width) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8g2_font.o(i.u8g2_string_width) refers to u8g2_font.o(i.u8g2_GetGlyphWidth) for u8g2_GetGlyphWidth
    u8g2_hvline.o(i.u8g2_DrawHLine) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_hvline.o(i.u8g2_DrawHVLine) refers to u8g2_hvline.o(i.u8g2_clip_intersection2) for u8g2_clip_intersection2
    u8g2_hvline.o(i.u8g2_DrawPixel) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_hvline.o(i.u8g2_DrawVLine) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8g2_font.o(i.u8g2_SetFontDirection) for u8g2_SetFontDirection
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8g2_font.o(i.u8g2_SetFontPosBaseline) for u8g2_SetFontPosBaseline
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8g2_font.o(i.u8g2_GetUTF8Width) for u8g2_GetUTF8Width
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8g2_buffer.o(i.u8g2_FirstPage) for u8g2_FirstPage
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8g2_selection_list.o(i.u8g2_DrawUTF8Lines) for u8g2_DrawUTF8Lines
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8x8_u8toa.o(i.u8x8_u8toa) for u8x8_u8toa
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8g2_buffer.o(i.u8g2_NextPage) for u8g2_NextPage
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8x8_debounce.o(i.u8x8_GetMenuEvent) for u8x8_GetMenuEvent
    u8g2_intersection.o(i.u8g2_IsIntersection) refers to u8g2_intersection.o(i.u8g2_is_intersection_decision_tree) for u8g2_is_intersection_decision_tree
    u8g2_line.o(i.u8g2_DrawLine) refers to u8g2_hvline.o(i.u8g2_DrawPixel) for u8g2_DrawPixel
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8g2_font.o(i.u8g2_SetFontDirection) for u8g2_SetFontDirection
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8g2_font.o(i.u8g2_SetFontPosBaseline) for u8g2_SetFontPosBaseline
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8g2_buffer.o(i.u8g2_FirstPage) for u8g2_FirstPage
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8g2_selection_list.o(i.u8g2_DrawUTF8Lines) for u8g2_DrawUTF8Lines
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8g2_selection_list.o(i.u8g2_DrawUTF8Line) for u8g2_DrawUTF8Line
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8g2_message.o(i.u8g2_draw_button_line) for u8g2_draw_button_line
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8g2_buffer.o(i.u8g2_NextPage) for u8g2_NextPage
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8x8_debounce.o(i.u8x8_GetMenuEvent) for u8x8_GetMenuEvent
    u8g2_message.o(i.u8g2_draw_button_line) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8g2_message.o(i.u8g2_draw_button_line) refers to u8x8_string.o(i.u8x8_GetStringLineStart) for u8x8_GetStringLineStart
    u8g2_message.o(i.u8g2_draw_button_line) refers to u8g2_font.o(i.u8g2_GetUTF8Width) for u8g2_GetUTF8Width
    u8g2_message.o(i.u8g2_draw_button_line) refers to u8g2_selection_list.o(i.u8g2_DrawUTF8Line) for u8g2_DrawUTF8Line
    u8g2_polygon.o(i.pg_DrawPolygon) refers to u8g2_polygon.o(i.pg_prepare) for pg_prepare
    u8g2_polygon.o(i.pg_DrawPolygon) refers to u8g2_polygon.o(i.pg_exec) for pg_exec
    u8g2_polygon.o(i.pg_exec) refers to u8g2_polygon.o(i.pg_line_init) for pg_line_init
    u8g2_polygon.o(i.pg_exec) refers to u8g2_polygon.o(i.pge_Next) for pge_Next
    u8g2_polygon.o(i.pg_exec) refers to u8g2_polygon.o(i.pg_hline) for pg_hline
    u8g2_polygon.o(i.pg_hline) refers to u8g2_hvline.o(i.u8g2_DrawHLine) for u8g2_DrawHLine
    u8g2_polygon.o(i.pg_line_init) refers to u8g2_polygon.o(i.pge_Init) for pge_Init
    u8g2_polygon.o(i.pg_prepare) refers to u8g2_polygon.o(i.pg_expand_min_y) for pg_expand_min_y
    u8g2_polygon.o(i.pg_prepare) refers to u8g2_polygon.o(i.pg_inc) for pg_inc
    u8g2_polygon.o(i.pg_prepare) refers to u8g2_polygon.o(i.pg_dec) for pg_dec
    u8g2_polygon.o(i.u8g2_AddPolygonXY) refers to u8g2_polygon.o(i.pg_AddPolygonXY) for pg_AddPolygonXY
    u8g2_polygon.o(i.u8g2_AddPolygonXY) refers to u8g2_polygon.o(.bss) for u8g2_pg
    u8g2_polygon.o(i.u8g2_ClearPolygonXY) refers to u8g2_polygon.o(i.pg_ClearPolygonXY) for pg_ClearPolygonXY
    u8g2_polygon.o(i.u8g2_ClearPolygonXY) refers to u8g2_polygon.o(.bss) for u8g2_pg
    u8g2_polygon.o(i.u8g2_DrawPolygon) refers to u8g2_polygon.o(i.pg_DrawPolygon) for pg_DrawPolygon
    u8g2_polygon.o(i.u8g2_DrawPolygon) refers to u8g2_polygon.o(.bss) for u8g2_pg
    u8g2_polygon.o(i.u8g2_DrawTriangle) refers to u8g2_polygon.o(i.u8g2_ClearPolygonXY) for u8g2_ClearPolygonXY
    u8g2_polygon.o(i.u8g2_DrawTriangle) refers to u8g2_polygon.o(i.u8g2_AddPolygonXY) for u8g2_AddPolygonXY
    u8g2_polygon.o(i.u8g2_DrawTriangle) refers to u8g2_polygon.o(i.u8g2_DrawPolygon) for u8g2_DrawPolygon
    u8g2_selection_list.o(i.u8g2_DrawSelectionList) refers to u8g2_selection_list.o(i.u8g2_draw_selection_list_line) for u8g2_draw_selection_list_line
    u8g2_selection_list.o(i.u8g2_DrawUTF8Line) refers to u8g2_font.o(i.u8g2_SetFontDirection) for u8g2_SetFontDirection
    u8g2_selection_list.o(i.u8g2_DrawUTF8Line) refers to u8g2_font.o(i.u8g2_GetUTF8Width) for u8g2_GetUTF8Width
    u8g2_selection_list.o(i.u8g2_DrawUTF8Line) refers to u8g2_font.o(i.u8g2_GetXOffsetUTF8) for u8g2_GetXOffsetUTF8
    u8g2_selection_list.o(i.u8g2_DrawUTF8Line) refers to u8g2_hvline.o(i.u8g2_SetDrawColor) for u8g2_SetDrawColor
    u8g2_selection_list.o(i.u8g2_DrawUTF8Line) refers to u8g2_box.o(i.u8g2_DrawBox) for u8g2_DrawBox
    u8g2_selection_list.o(i.u8g2_DrawUTF8Line) refers to u8g2_box.o(i.u8g2_DrawFrame) for u8g2_DrawFrame
    u8g2_selection_list.o(i.u8g2_DrawUTF8Line) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    u8g2_selection_list.o(i.u8g2_DrawUTF8Lines) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8g2_selection_list.o(i.u8g2_DrawUTF8Lines) refers to u8x8_string.o(i.u8x8_GetStringLineStart) for u8x8_GetStringLineStart
    u8g2_selection_list.o(i.u8g2_DrawUTF8Lines) refers to u8g2_selection_list.o(i.u8g2_DrawUTF8Line) for u8g2_DrawUTF8Line
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8g2_font.o(i.u8g2_SetFontPosBaseline) for u8g2_SetFontPosBaseline
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8g2_buffer.o(i.u8g2_FirstPage) for u8g2_FirstPage
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8g2_selection_list.o(i.u8g2_DrawUTF8Lines) for u8g2_DrawUTF8Lines
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8g2_hvline.o(i.u8g2_DrawHLine) for u8g2_DrawHLine
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8g2_selection_list.o(i.u8g2_DrawSelectionList) for u8g2_DrawSelectionList
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8g2_buffer.o(i.u8g2_NextPage) for u8g2_NextPage
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8x8_debounce.o(i.u8x8_GetMenuEvent) for u8x8_GetMenuEvent
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8x8_selection_list.o(i.u8sl_Next) for u8sl_Next
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8x8_selection_list.o(i.u8sl_Prev) for u8sl_Prev
    u8g2_selection_list.o(i.u8g2_draw_selection_list_line) refers to u8x8_string.o(i.u8x8_GetStringLineStart) for u8x8_GetStringLineStart
    u8g2_selection_list.o(i.u8g2_draw_selection_list_line) refers to u8g2_selection_list.o(i.u8g2_DrawUTF8Line) for u8g2_DrawUTF8Line
    u8g2_setup.o(i.u8g2_SendF) refers to u8x8_cad.o(i.u8x8_cad_vsendf) for u8x8_cad_vsendf
    u8g2_setup.o(i.u8g2_SetupBuffer) refers to u8g2_setup.o(i.u8g2_SetMaxClipWindow) for u8g2_SetMaxClipWindow
    u8g2_setup.o(i.u8g2_SetupBuffer) refers to u8g2_font.o(i.u8g2_SetFontPosBaseline) for u8g2_SetFontPosBaseline
    u8g2_setup.o(i.u8g2_Setup_null) refers to u8x8_setup.o(i.u8x8_Setup) for u8x8_Setup
    u8g2_setup.o(i.u8g2_Setup_null) refers to u8g2_setup.o(i.u8g2_SetupBuffer) for u8g2_SetupBuffer
    u8g2_setup.o(i.u8g2_Setup_null) refers to u8x8_cad.o(i.u8x8_cad_empty) for u8x8_cad_empty
    u8g2_setup.o(i.u8g2_Setup_null) refers to u8x8_setup.o(i.u8x8_d_null_cb) for u8x8_d_null_cb
    u8g2_setup.o(i.u8g2_Setup_null) refers to u8g2_ll_hvline.o(i.u8g2_ll_hvline_vertical_top_lsb) for u8g2_ll_hvline_vertical_top_lsb
    u8g2_setup.o(i.u8g2_Setup_null) refers to u8g2_setup.o(.data) for buf
    u8g2_setup.o(i.u8g2_apply_clip_window) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_setup.o(i.u8g2_draw_l90_mirrorr_r0) refers to u8g2_hvline.o(i.u8g2_draw_hv_line_2dir) for u8g2_draw_hv_line_2dir
    u8g2_setup.o(i.u8g2_draw_l90_r0) refers to u8g2_hvline.o(i.u8g2_draw_hv_line_2dir) for u8g2_draw_hv_line_2dir
    u8g2_setup.o(i.u8g2_draw_l90_r1) refers to u8g2_hvline.o(i.u8g2_draw_hv_line_2dir) for u8g2_draw_hv_line_2dir
    u8g2_setup.o(i.u8g2_draw_l90_r2) refers to u8g2_hvline.o(i.u8g2_draw_hv_line_2dir) for u8g2_draw_hv_line_2dir
    u8g2_setup.o(i.u8g2_draw_l90_r3) refers to u8g2_hvline.o(i.u8g2_draw_hv_line_2dir) for u8g2_draw_hv_line_2dir
    u8g2_setup.o(i.u8g2_draw_mirror_vertical_r0) refers to u8g2_hvline.o(i.u8g2_draw_hv_line_2dir) for u8g2_draw_hv_line_2dir
    u8g2_setup.o(i.u8g2_update_dimension_r0) refers to u8g2_setup.o(i.u8g2_update_dimension_common) for u8g2_update_dimension_common
    u8g2_setup.o(i.u8g2_update_dimension_r1) refers to u8g2_setup.o(i.u8g2_update_dimension_common) for u8g2_update_dimension_common
    u8g2_setup.o(i.u8g2_update_dimension_r2) refers to u8g2_setup.o(i.u8g2_update_dimension_common) for u8g2_update_dimension_common
    u8g2_setup.o(i.u8g2_update_dimension_r3) refers to u8g2_setup.o(i.u8g2_update_dimension_common) for u8g2_update_dimension_common
    u8g2_setup.o(i.u8g2_update_page_win_r0) refers to u8g2_setup.o(i.u8g2_apply_clip_window) for u8g2_apply_clip_window
    u8g2_setup.o(i.u8g2_update_page_win_r1) refers to u8g2_setup.o(i.u8g2_apply_clip_window) for u8g2_apply_clip_window
    u8g2_setup.o(i.u8g2_update_page_win_r2) refers to u8g2_setup.o(i.u8g2_apply_clip_window) for u8g2_apply_clip_window
    u8g2_setup.o(i.u8g2_update_page_win_r3) refers to u8g2_setup.o(i.u8g2_apply_clip_window) for u8g2_apply_clip_window
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_dimension_r0) for u8g2_update_dimension_r0
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_page_win_r0) for u8g2_update_page_win_r0
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_draw_l90_r0) for u8g2_draw_l90_r0
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_dimension_r1) for u8g2_update_dimension_r1
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_page_win_r1) for u8g2_update_page_win_r1
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_draw_l90_r1) for u8g2_draw_l90_r1
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_dimension_r2) for u8g2_update_dimension_r2
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_page_win_r2) for u8g2_update_page_win_r2
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_draw_l90_r2) for u8g2_draw_l90_r2
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_dimension_r3) for u8g2_update_dimension_r3
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_page_win_r3) for u8g2_update_page_win_r3
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_draw_l90_r3) for u8g2_draw_l90_r3
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_draw_l90_mirrorr_r0) for u8g2_draw_l90_mirrorr_r0
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_draw_mirror_vertical_r0) for u8g2_draw_mirror_vertical_r0
    u8log.o(i.u8log_Init) refers to memseta.o(.text) for __aeabi_memclr4
    u8log.o(i.u8log_Init) refers to u8log.o(i.u8log_clear_screen) for u8log_clear_screen
    u8log.o(i.u8log_WriteChar) refers to u8log.o(i.u8log_write_char) for u8log_write_char
    u8log.o(i.u8log_WriteDec16) refers to u8x8_u16toa.o(i.u8x8_u16toa) for u8x8_u16toa
    u8log.o(i.u8log_WriteDec16) refers to u8log.o(i.u8log_WriteString) for u8log_WriteString
    u8log.o(i.u8log_WriteDec8) refers to u8x8_u8toa.o(i.u8x8_u8toa) for u8x8_u8toa
    u8log.o(i.u8log_WriteDec8) refers to u8log.o(i.u8log_WriteString) for u8log_WriteString
    u8log.o(i.u8log_WriteHex16) refers to u8log.o(i.u8log_WriteHex8) for u8log_WriteHex8
    u8log.o(i.u8log_WriteHex32) refers to u8log.o(i.u8log_WriteHex16) for u8log_WriteHex16
    u8log.o(i.u8log_WriteHex8) refers to u8log.o(i.u8log_WriteHexHalfByte) for u8log_WriteHexHalfByte
    u8log.o(i.u8log_WriteHexHalfByte) refers to u8log.o(i.u8log_WriteChar) for u8log_WriteChar
    u8log.o(i.u8log_WriteString) refers to u8log.o(i.u8log_WriteChar) for u8log_WriteChar
    u8log.o(i.u8log_cursor_on_screen) refers to u8log.o(i.u8log_scroll_up) for u8log_scroll_up
    u8log.o(i.u8log_write_char) refers to u8log.o(i.u8log_cursor_on_screen) for u8log_cursor_on_screen
    u8log.o(i.u8log_write_char) refers to u8log.o(i.u8log_clear_screen) for u8log_clear_screen
    u8log.o(i.u8log_write_char) refers to u8log.o(i.u8log_write_to_screen) for u8log_write_to_screen
    u8log.o(i.u8log_write_to_screen) refers to u8log.o(i.u8log_cursor_on_screen) for u8log_cursor_on_screen
    u8log_u8g2.o(i.u8g2_DrawLog) refers to u8g2_font.o(i.u8g2_SetFontDirection) for u8g2_SetFontDirection
    u8log_u8g2.o(i.u8g2_DrawLog) refers to u8g2_font.o(i.u8g2_DrawGlyph) for u8g2_DrawGlyph
    u8log_u8g2.o(i.u8log_u8g2_cb) refers to u8g2_buffer.o(i.u8g2_FirstPage) for u8g2_FirstPage
    u8log_u8g2.o(i.u8log_u8g2_cb) refers to u8log_u8g2.o(i.u8g2_DrawLog) for u8g2_DrawLog
    u8log_u8g2.o(i.u8log_u8g2_cb) refers to u8g2_buffer.o(i.u8g2_NextPage) for u8g2_NextPage
    u8log_u8x8.o(i.u8log_u8x8_cb) refers to u8log_u8x8.o(i.u8x8_DrawLog) for u8x8_DrawLog
    u8log_u8x8.o(i.u8log_u8x8_cb) refers to u8log_u8x8.o(i.u8x8_DrawLogLine) for u8x8_DrawLogLine
    u8log_u8x8.o(i.u8x8_DrawLog) refers to u8log_u8x8.o(i.u8x8_DrawLogLine) for u8x8_DrawLogLine
    u8log_u8x8.o(i.u8x8_DrawLogLine) refers to u8x8_8x8.o(i.u8x8_DrawGlyph) for u8x8_DrawGlyph
    u8x8_8x8.o(i.u8x8_Draw1x2Glyph) refers to u8x8_8x8.o(i.u8x8_draw_1x2_subglyph) for u8x8_draw_1x2_subglyph
    u8x8_8x8.o(i.u8x8_Draw1x2String) refers to u8x8_8x8.o(i.u8x8_draw_1x2_string) for u8x8_draw_1x2_string
    u8x8_8x8.o(i.u8x8_Draw1x2String) refers to u8x8_8x8.o(i.u8x8_ascii_next) for u8x8_ascii_next
    u8x8_8x8.o(i.u8x8_Draw1x2UTF8) refers to u8x8_8x8.o(i.u8x8_draw_1x2_string) for u8x8_draw_1x2_string
    u8x8_8x8.o(i.u8x8_Draw1x2UTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8x8_8x8.o(i.u8x8_Draw2x2Glyph) refers to u8x8_8x8.o(i.u8x8_draw_2x2_subglyph) for u8x8_draw_2x2_subglyph
    u8x8_8x8.o(i.u8x8_Draw2x2String) refers to u8x8_8x8.o(i.u8x8_draw_2x2_string) for u8x8_draw_2x2_string
    u8x8_8x8.o(i.u8x8_Draw2x2String) refers to u8x8_8x8.o(i.u8x8_ascii_next) for u8x8_ascii_next
    u8x8_8x8.o(i.u8x8_Draw2x2UTF8) refers to u8x8_8x8.o(i.u8x8_draw_2x2_string) for u8x8_draw_2x2_string
    u8x8_8x8.o(i.u8x8_Draw2x2UTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8x8_8x8.o(i.u8x8_DrawGlyph) refers to u8x8_8x8.o(i.u8x8_get_glyph_data) for u8x8_get_glyph_data
    u8x8_8x8.o(i.u8x8_DrawGlyph) refers to u8x8_display.o(i.u8x8_DrawTile) for u8x8_DrawTile
    u8x8_8x8.o(i.u8x8_DrawString) refers to u8x8_8x8.o(i.u8x8_draw_string) for u8x8_draw_string
    u8x8_8x8.o(i.u8x8_DrawString) refers to u8x8_8x8.o(i.u8x8_ascii_next) for u8x8_ascii_next
    u8x8_8x8.o(i.u8x8_DrawUTF8) refers to u8x8_8x8.o(i.u8x8_draw_string) for u8x8_draw_string
    u8x8_8x8.o(i.u8x8_DrawUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8x8_8x8.o(i.u8x8_GetUTF8Len) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8x8_8x8.o(i.u8x8_GetUTF8Len) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8x8_8x8.o(i.u8x8_draw_1x2_string) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8x8_8x8.o(i.u8x8_draw_1x2_string) refers to u8x8_8x8.o(i.u8x8_Draw1x2Glyph) for u8x8_Draw1x2Glyph
    u8x8_8x8.o(i.u8x8_draw_1x2_subglyph) refers to u8x8_8x8.o(i.u8x8_get_glyph_data) for u8x8_get_glyph_data
    u8x8_8x8.o(i.u8x8_draw_1x2_subglyph) refers to u8x8_8x8.o(i.u8x8_upscale_byte) for u8x8_upscale_byte
    u8x8_8x8.o(i.u8x8_draw_1x2_subglyph) refers to u8x8_display.o(i.u8x8_DrawTile) for u8x8_DrawTile
    u8x8_8x8.o(i.u8x8_draw_2x2_string) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8x8_8x8.o(i.u8x8_draw_2x2_string) refers to u8x8_8x8.o(i.u8x8_Draw2x2Glyph) for u8x8_Draw2x2Glyph
    u8x8_8x8.o(i.u8x8_draw_2x2_subglyph) refers to u8x8_8x8.o(i.u8x8_get_glyph_data) for u8x8_get_glyph_data
    u8x8_8x8.o(i.u8x8_draw_2x2_subglyph) refers to u8x8_8x8.o(i.u8x8_upscale_byte) for u8x8_upscale_byte
    u8x8_8x8.o(i.u8x8_draw_2x2_subglyph) refers to u8x8_8x8.o(i.u8x8_upscale_buf) for u8x8_upscale_buf
    u8x8_8x8.o(i.u8x8_draw_2x2_subglyph) refers to u8x8_display.o(i.u8x8_DrawTile) for u8x8_DrawTile
    u8x8_8x8.o(i.u8x8_draw_string) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8x8_8x8.o(i.u8x8_draw_string) refers to u8x8_8x8.o(i.u8x8_DrawGlyph) for u8x8_DrawGlyph
    u8x8_byte.o(i.i2c_clear_scl) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.i2c_clear_sda) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.i2c_delay) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.i2c_init) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.i2c_init) refers to u8x8_byte.o(i.i2c_delay) for i2c_delay
    u8x8_byte.o(i.i2c_read_bit) refers to u8x8_byte.o(i.i2c_read_sda) for i2c_read_sda
    u8x8_byte.o(i.i2c_read_bit) refers to u8x8_byte.o(i.i2c_delay) for i2c_delay
    u8x8_byte.o(i.i2c_read_bit) refers to u8x8_byte.o(i.i2c_read_scl_and_delay) for i2c_read_scl_and_delay
    u8x8_byte.o(i.i2c_read_bit) refers to u8x8_byte.o(i.i2c_clear_scl) for i2c_clear_scl
    u8x8_byte.o(i.i2c_read_scl_and_delay) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.i2c_read_scl_and_delay) refers to u8x8_byte.o(i.i2c_delay) for i2c_delay
    u8x8_byte.o(i.i2c_read_sda) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.i2c_start) refers to u8x8_byte.o(i.i2c_read_sda) for i2c_read_sda
    u8x8_byte.o(i.i2c_start) refers to u8x8_byte.o(i.i2c_delay) for i2c_delay
    u8x8_byte.o(i.i2c_start) refers to u8x8_byte.o(i.i2c_read_scl_and_delay) for i2c_read_scl_and_delay
    u8x8_byte.o(i.i2c_start) refers to u8x8_byte.o(i.i2c_clear_sda) for i2c_clear_sda
    u8x8_byte.o(i.i2c_start) refers to u8x8_byte.o(i.i2c_clear_scl) for i2c_clear_scl
    u8x8_byte.o(i.i2c_stop) refers to u8x8_byte.o(i.i2c_clear_sda) for i2c_clear_sda
    u8x8_byte.o(i.i2c_stop) refers to u8x8_byte.o(i.i2c_delay) for i2c_delay
    u8x8_byte.o(i.i2c_stop) refers to u8x8_byte.o(i.i2c_read_scl_and_delay) for i2c_read_scl_and_delay
    u8x8_byte.o(i.i2c_stop) refers to u8x8_byte.o(i.i2c_read_sda) for i2c_read_sda
    u8x8_byte.o(i.i2c_write_bit) refers to u8x8_byte.o(i.i2c_read_sda) for i2c_read_sda
    u8x8_byte.o(i.i2c_write_bit) refers to u8x8_byte.o(i.i2c_clear_sda) for i2c_clear_sda
    u8x8_byte.o(i.i2c_write_bit) refers to u8x8_byte.o(i.i2c_delay) for i2c_delay
    u8x8_byte.o(i.i2c_write_bit) refers to u8x8_byte.o(i.i2c_read_scl_and_delay) for i2c_read_scl_and_delay
    u8x8_byte.o(i.i2c_write_bit) refers to u8x8_byte.o(i.i2c_clear_scl) for i2c_clear_scl
    u8x8_byte.o(i.i2c_write_byte) refers to u8x8_byte.o(i.i2c_write_bit) for i2c_write_bit
    u8x8_byte.o(i.i2c_write_byte) refers to u8x8_byte.o(i.i2c_read_bit) for i2c_read_bit
    u8x8_byte.o(i.u8x8_byte_3wire_sw_spi) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.u8x8_byte_3wire_sw_spi) refers to u8x8_byte.o(.data) for last_dc
    u8x8_byte.o(i.u8x8_byte_4wire_sw_spi) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.u8x8_byte_8bit_6800mode) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.u8x8_byte_8bit_8080mode) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.u8x8_byte_SendByte) refers to u8x8_byte.o(i.u8x8_byte_SendBytes) for u8x8_byte_SendBytes
    u8x8_byte.o(i.u8x8_byte_ks0108) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.u8x8_byte_ks0108) refers to u8x8_byte.o(i.u8x8_byte_set_ks0108_cs) for u8x8_byte_set_ks0108_cs
    u8x8_byte.o(i.u8x8_byte_sed1520) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.u8x8_byte_sed1520) refers to u8x8_byte.o(.data) for enable_pin
    u8x8_byte.o(i.u8x8_byte_set_ks0108_cs) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.u8x8_byte_sw_i2c) refers to u8x8_byte.o(i.i2c_write_byte) for i2c_write_byte
    u8x8_byte.o(i.u8x8_byte_sw_i2c) refers to u8x8_byte.o(i.i2c_init) for i2c_init
    u8x8_byte.o(i.u8x8_byte_sw_i2c) refers to u8x8_byte.o(i.i2c_start) for i2c_start
    u8x8_byte.o(i.u8x8_byte_sw_i2c) refers to u8x8_byte.o(i.i2c_stop) for i2c_stop
    u8x8_cad.o(i.u8x8_SendF) refers to u8x8_cad.o(i.u8x8_cad_vsendf) for u8x8_cad_vsendf
    u8x8_cad.o(i.u8x8_cad_001) refers to u8x8_byte.o(i.u8x8_byte_SetDC) for u8x8_byte_SetDC
    u8x8_cad.o(i.u8x8_cad_001) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_011) refers to u8x8_byte.o(i.u8x8_byte_SetDC) for u8x8_byte_SetDC
    u8x8_cad.o(i.u8x8_cad_011) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_011_ssd13xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_cad_011_ssd13xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_011_ssd13xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_cad_011_ssd13xx_i2c) refers to u8x8_cad.o(i.u8x8_i2c_data_transfer) for u8x8_i2c_data_transfer
    u8x8_cad.o(i.u8x8_cad_100) refers to u8x8_byte.o(i.u8x8_byte_SetDC) for u8x8_byte_SetDC
    u8x8_cad.o(i.u8x8_cad_100) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_110) refers to u8x8_byte.o(i.u8x8_byte_SetDC) for u8x8_byte_SetDC
    u8x8_cad.o(i.u8x8_cad_110) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_SendSequence) refers to u8x8_cad.o(i.u8x8_cad_SendData) for u8x8_cad_SendData
    u8x8_cad.o(i.u8x8_cad_SendSequence) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_cad.o(i.u8x8_cad_empty) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_ld7032_i2c) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_cad_ld7032_i2c) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_cad_ld7032_i2c) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_ld7032_i2c) refers to u8x8_cad.o(.data) for in_transfer
    u8x8_cad.o(i.u8x8_cad_ssd13xx_fast_i2c) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_cad_ssd13xx_fast_i2c) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_cad_ssd13xx_fast_i2c) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_ssd13xx_fast_i2c) refers to u8x8_cad.o(i.u8x8_i2c_data_transfer) for u8x8_i2c_data_transfer
    u8x8_cad.o(i.u8x8_cad_ssd13xx_fast_i2c) refers to u8x8_cad.o(.data) for in_transfer
    u8x8_cad.o(i.u8x8_cad_ssd13xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_cad_ssd13xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_ssd13xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_cad_ssd13xx_i2c) refers to u8x8_cad.o(i.u8x8_i2c_data_transfer) for u8x8_i2c_data_transfer
    u8x8_cad.o(i.u8x8_cad_st75256_i2c) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_cad_st75256_i2c) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_st75256_i2c) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_cad_st75256_i2c) refers to u8x8_cad.o(i.u8x8_i2c_data_transfer) for u8x8_i2c_data_transfer
    u8x8_cad.o(i.u8x8_cad_st7920_spi) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_st7920_spi) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_cad.o(i.u8x8_cad_st7920_spi) refers to u8x8_byte.o(i.u8x8_byte_SendBytes) for u8x8_byte_SendBytes
    u8x8_cad.o(i.u8x8_cad_st7920_spi) refers to u8x8_cad.o(.bss) for buf
    u8x8_cad.o(i.u8x8_cad_uc1638_i2c) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_cad_uc1638_i2c) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_cad_uc1638_i2c) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_uc1638_i2c) refers to u8x8_cad.o(.data) for in_transfer
    u8x8_cad.o(i.u8x8_cad_uc16xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_cad_uc16xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_cad_uc16xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_uc16xx_i2c) refers to u8x8_cad.o(.data) for in_transfer
    u8x8_cad.o(i.u8x8_cad_vsendf) refers to u8x8_cad.o(i.u8x8_cad_StartTransfer) for u8x8_cad_StartTransfer
    u8x8_cad.o(i.u8x8_cad_vsendf) refers to u8x8_cad.o(i.u8x8_cad_SendArg) for u8x8_cad_SendArg
    u8x8_cad.o(i.u8x8_cad_vsendf) refers to u8x8_cad.o(i.u8x8_cad_SendCmd) for u8x8_cad_SendCmd
    u8x8_cad.o(i.u8x8_cad_vsendf) refers to u8x8_cad.o(i.u8x8_cad_SendData) for u8x8_cad_SendData
    u8x8_cad.o(i.u8x8_cad_vsendf) refers to u8x8_cad.o(i.u8x8_cad_EndTransfer) for u8x8_cad_EndTransfer
    u8x8_cad.o(i.u8x8_gu800_cad_110) refers to u8x8_byte.o(i.u8x8_byte_SetDC) for u8x8_byte_SetDC
    u8x8_cad.o(i.u8x8_gu800_cad_110) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_gu800_cad_110) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_gu800_cad_110) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_i2c_data_transfer) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_i2c_data_transfer) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_i2c_data_transfer) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_capture.o(i.u8x8_capture_write_pbm_pre) refers to u8x8_u16toa.o(i.u8x8_utoa) for u8x8_utoa
    u8x8_capture.o(i.u8x8_capture_write_xbm_pre) refers to u8x8_u16toa.o(i.u8x8_utoa) for u8x8_utoa
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_128x64_noname) refers to u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_generic) for u8x8_d_ssd1315_generic
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_128x64_noname) refers to u8x8_display.o(i.u8x8_d_helper_display_init) for u8x8_d_helper_display_init
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_128x64_noname) refers to u8x8_cad.o(i.u8x8_cad_SendSequence) for u8x8_cad_SendSequence
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_128x64_noname) refers to u8x8_display.o(i.u8x8_d_helper_display_setup_memory) for u8x8_d_helper_display_setup_memory
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_128x64_noname) refers to u8x8_d_ssd1315_128x64_noname.o(.constdata) for u8x8_d_ssd1315_128x64_noname_init_seq
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_generic) refers to u8x8_cad.o(i.u8x8_cad_SendSequence) for u8x8_cad_SendSequence
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_generic) refers to u8x8_cad.o(i.u8x8_cad_StartTransfer) for u8x8_cad_StartTransfer
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_generic) refers to u8x8_cad.o(i.u8x8_cad_SendCmd) for u8x8_cad_SendCmd
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_generic) refers to u8x8_cad.o(i.u8x8_cad_SendArg) for u8x8_cad_SendArg
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_generic) refers to u8x8_cad.o(i.u8x8_cad_EndTransfer) for u8x8_cad_EndTransfer
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_generic) refers to u8x8_cad.o(i.u8x8_cad_SendData) for u8x8_cad_SendData
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_generic) refers to u8x8_d_ssd1315_128x64_noname.o(.constdata) for u8x8_d_ssd1315_128x64_noname_powersave0_seq
    u8x8_debounce.o(i.u8x8_GetMenuEvent) refers to u8x8_debounce.o(i.u8x8_read_pin_state) for u8x8_read_pin_state
    u8x8_debounce.o(i.u8x8_GetMenuEvent) refers to u8x8_debounce.o(i.u8x8_find_first_diff) for u8x8_find_first_diff
    u8x8_debounce.o(i.u8x8_read_pin_state) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_display.o(i.u8x8_ClearDisplay) refers to u8x8_display.o(i.u8x8_ClearDisplayWithTile) for u8x8_ClearDisplayWithTile
    u8x8_display.o(i.u8x8_FillDisplay) refers to u8x8_display.o(i.u8x8_ClearDisplayWithTile) for u8x8_ClearDisplayWithTile
    u8x8_display.o(i.u8x8_d_helper_display_init) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_input_value.o(i.u8x8_UserInterfaceInputValue) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8x8_input_value.o(i.u8x8_UserInterfaceInputValue) refers to u8x8_8x8.o(i.u8x8_GetUTF8Len) for u8x8_GetUTF8Len
    u8x8_input_value.o(i.u8x8_UserInterfaceInputValue) refers to u8x8_display.o(i.u8x8_ClearDisplay) for u8x8_ClearDisplay
    u8x8_input_value.o(i.u8x8_UserInterfaceInputValue) refers to u8x8_string.o(i.u8x8_DrawUTF8Lines) for u8x8_DrawUTF8Lines
    u8x8_input_value.o(i.u8x8_UserInterfaceInputValue) refers to u8x8_8x8.o(i.u8x8_DrawUTF8) for u8x8_DrawUTF8
    u8x8_input_value.o(i.u8x8_UserInterfaceInputValue) refers to u8x8_u8toa.o(i.u8x8_u8toa) for u8x8_u8toa
    u8x8_input_value.o(i.u8x8_UserInterfaceInputValue) refers to u8x8_debounce.o(i.u8x8_GetMenuEvent) for u8x8_GetMenuEvent
    u8x8_message.o(i.u8x8_UserInterfaceMessage) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8x8_message.o(i.u8x8_UserInterfaceMessage) refers to u8x8_display.o(i.u8x8_ClearDisplay) for u8x8_ClearDisplay
    u8x8_message.o(i.u8x8_UserInterfaceMessage) refers to u8x8_string.o(i.u8x8_DrawUTF8Lines) for u8x8_DrawUTF8Lines
    u8x8_message.o(i.u8x8_UserInterfaceMessage) refers to u8x8_string.o(i.u8x8_DrawUTF8Line) for u8x8_DrawUTF8Line
    u8x8_message.o(i.u8x8_UserInterfaceMessage) refers to u8x8_message.o(i.u8x8_draw_button_line) for u8x8_draw_button_line
    u8x8_message.o(i.u8x8_UserInterfaceMessage) refers to u8x8_debounce.o(i.u8x8_GetMenuEvent) for u8x8_GetMenuEvent
    u8x8_message.o(i.u8x8_draw_button_line) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8x8_message.o(i.u8x8_draw_button_line) refers to u8x8_string.o(i.u8x8_GetStringLineStart) for u8x8_GetStringLineStart
    u8x8_message.o(i.u8x8_draw_button_line) refers to u8x8_8x8.o(i.u8x8_GetUTF8Len) for u8x8_GetUTF8Len
    u8x8_message.o(i.u8x8_draw_button_line) refers to u8x8_8x8.o(i.u8x8_DrawUTF8) for u8x8_DrawUTF8
    u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList) refers to u8x8_string.o(i.u8x8_DrawUTF8Lines) for u8x8_DrawUTF8Lines
    u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList) refers to u8x8_selection_list.o(i.u8x8_DrawSelectionList) for u8x8_DrawSelectionList
    u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList) refers to u8x8_debounce.o(i.u8x8_GetMenuEvent) for u8x8_GetMenuEvent
    u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList) refers to u8x8_selection_list.o(i.u8sl_Next) for u8sl_Next
    u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList) refers to u8x8_selection_list.o(i.u8sl_Prev) for u8sl_Prev
    u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList) refers to u8x8_selection_list.o(i.u8x8_sl_string_line_cb) for u8x8_sl_string_line_cb
    u8x8_selection_list.o(i.u8x8_sl_string_line_cb) refers to u8x8_string.o(i.u8x8_GetStringLineStart) for u8x8_GetStringLineStart
    u8x8_selection_list.o(i.u8x8_sl_string_line_cb) refers to u8x8_string.o(i.u8x8_DrawUTF8Line) for u8x8_DrawUTF8Line
    u8x8_setup.o(i.u8x8_Setup) refers to u8x8_setup.o(i.u8x8_SetupDefaults) for u8x8_SetupDefaults
    u8x8_setup.o(i.u8x8_Setup) refers to u8x8_display.o(i.u8x8_SetupMemory) for u8x8_SetupMemory
    u8x8_setup.o(i.u8x8_SetupDefaults) refers to u8x8_setup.o(i.u8x8_dummy_cb) for u8x8_dummy_cb
    u8x8_setup.o(i.u8x8_d_null_cb) refers to u8x8_display.o(i.u8x8_d_helper_display_setup_memory) for u8x8_d_helper_display_setup_memory
    u8x8_setup.o(i.u8x8_d_null_cb) refers to u8x8_display.o(i.u8x8_d_helper_display_init) for u8x8_d_helper_display_init
    u8x8_setup.o(i.u8x8_d_null_cb) refers to u8x8_setup.o(.constdata) for u8x8_null_display_info
    u8x8_string.o(i.u8x8_CopyStringLine) refers to u8x8_string.o(i.u8x8_GetStringLineStart) for u8x8_GetStringLineStart
    u8x8_string.o(i.u8x8_DrawUTF8Line) refers to u8x8_8x8.o(i.u8x8_GetUTF8Len) for u8x8_GetUTF8Len
    u8x8_string.o(i.u8x8_DrawUTF8Line) refers to u8x8_8x8.o(i.u8x8_DrawUTF8) for u8x8_DrawUTF8
    u8x8_string.o(i.u8x8_DrawUTF8Lines) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8x8_string.o(i.u8x8_DrawUTF8Lines) refers to u8x8_string.o(i.u8x8_GetStringLineStart) for u8x8_GetStringLineStart
    u8x8_string.o(i.u8x8_DrawUTF8Lines) refers to u8x8_string.o(i.u8x8_DrawUTF8Line) for u8x8_DrawUTF8Line
    u8x8_u8toa.o(i.u8x8_s8toa) refers to u8x8_u8toa.o(i.u8x8_u8toap) for u8x8_u8toap
    u8x8_u8toa.o(i.u8x8_s8toa) refers to u8x8_u8toa.o(.data) for buf
    u8x8_u8toa.o(i.u8x8_u8toa) refers to u8x8_u8toa.o(i.u8x8_u8toap) for u8x8_u8toap
    u8x8_u8toa.o(i.u8x8_u8toa) refers to u8x8_u8toa.o(.data) for buf
    u8x8_u8toa.o(i.u8x8_u8toap) refers to u8x8_u8toa.o(.constdata) for u8x8_u8toa_tab
    u8x8_u8toa.o(i.u8x8_u8tox) refers to memcpya.o(.text) for __aeabi_memcpy4
    u8x8_u8toa.o(i.u8x8_u8tox) refers to u8x8_u8toa.o(.data) for buf
    u8x8_u16toa.o(i.u8x8_u16toa) refers to u8x8_u16toa.o(i.u8x8_u16toap) for u8x8_u16toap
    u8x8_u16toa.o(i.u8x8_u16toa) refers to u8x8_u16toa.o(.data) for buf
    u8x8_u16toa.o(i.u8x8_utoa) refers to u8x8_u16toa.o(i.u8x8_u16toa) for u8x8_u16toa
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cfcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f103xe.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f103xe.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f103xe.o(HEAP), (512 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (60 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (60 bytes).
    Removing stm32f1xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_ConfigEventout), (24 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_DisableEventout), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_EnableEventout), (20 bytes).
    Removing stm32f1xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_DeInit), (60 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (70 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (224 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (70 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetError), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetMode), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetState), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (440 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (126 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (876 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (452 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (244 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (676 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (364 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (548 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (288 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (452 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (232 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (884 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (604 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (264 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (532 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (244 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (384 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (300 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (152 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (460 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (158 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (460 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (158 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (404 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (300 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (152 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_DMAError), (70 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt), (318 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead), (312 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead), (352 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Slave_AF), (168 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (138 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout), (98 bytes).
    Removing stm32f1xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (20 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (20 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (20 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (20 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (20 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (20 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DeInit), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_ResumeTick), (18 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SetTickFreq), (48 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SuspendTick), (18 bytes).
    Removing stm32f1xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit), (272 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (68 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (200 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (100 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (32 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (84 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (280 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (304 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.constdata), (18 bytes).
    Removing stm32f1xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit), (340 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (28 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_LockPin), (46 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (20 bytes).
    Removing stm32f1xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit), (140 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetError), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (1620 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (90 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start), (114 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (106 bytes).
    Removing stm32f1xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (68 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetActive), (48 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (48 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (148 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (68 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (40 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (180 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DeInit), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (20 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (20 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (20 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (20 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (40 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (84 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe), (6 bytes).
    Removing stm32f1xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord), (32 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode), (132 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation), (112 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (376 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (8 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (44 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program), (176 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT), (100 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock), (48 bytes).
    Removing stm32f1xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase), (40 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (232 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (236 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (28 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData), (76 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (124 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase), (40 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (80 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase), (96 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (28 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetUserData), (40 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (200 bytes).
    Removing stm32f1xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (120 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearPending), (28 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (156 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetPending), (36 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (44 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (240 bytes).
    Removing stm32f1xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (68 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (68 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init), (122 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_Init), (146 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_SendBreak), (80 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (82 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (82 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init), (158 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (6 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (296 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (114 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort), (278 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive), (194 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (216 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit), (124 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (144 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT), (328 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAPause), (166 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAResume), (170 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DeInit), (62 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetError), (6 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetState), (20 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive), (194 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (140 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_IT), (56 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback), (56 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (28 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt), (88 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback), (56 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt), (14 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (24 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT), (64 bytes).
    Removing system_stm32f1xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f1xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f1xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f1xx.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing key_app.o(.rev16_text), (4 bytes).
    Removing key_app.o(.revsh_text), (4 bytes).
    Removing key_app.o(.rrx_text), (6 bytes).
    Removing led_app.o(.rev16_text), (4 bytes).
    Removing led_app.o(.revsh_text), (4 bytes).
    Removing led_app.o(.rrx_text), (6 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing scheduler.o(.rrx_text), (6 bytes).
    Removing btn_app.o(.rev16_text), (4 bytes).
    Removing btn_app.o(.revsh_text), (4 bytes).
    Removing btn_app.o(.rrx_text), (6 bytes).
    Removing usart_app.o(.rev16_text), (4 bytes).
    Removing usart_app.o(.revsh_text), (4 bytes).
    Removing usart_app.o(.rrx_text), (6 bytes).
    Removing oled_app.o(.rev16_text), (4 bytes).
    Removing oled_app.o(.revsh_text), (4 bytes).
    Removing oled_app.o(.rrx_text), (6 bytes).
    Removing oled_app.o(i.oled_printf), (60 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_add_btn), (32 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_remove_btn), (32 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_remove_btn_by_idx), (32 bytes).
    Removing ebtn.o(i.ebtn_combo_register), (60 bytes).
    Removing ebtn.o(i.ebtn_get_btn_by_key_id), (80 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_btn), (12 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_btn_dyn), (12 bytes).
    Removing ebtn.o(i.ebtn_get_config), (12 bytes).
    Removing ebtn.o(i.ebtn_get_total_btn_cnt), (28 bytes).
    Removing ebtn.o(i.ebtn_is_btn_active), (20 bytes).
    Removing ebtn.o(i.ebtn_is_btn_in_process), (20 bytes).
    Removing ebtn.o(i.ebtn_is_in_process), (132 bytes).
    Removing ebtn.o(i.ebtn_register), (72 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_getchar), (160 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_peek), (172 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_put_force), (304 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar), (164 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar_force), (200 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_reset), (112 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_Allfill), (56 bytes).
    Removing oled.o(i.OLED_Display_Off), (22 bytes).
    Removing oled.o(i.OLED_Display_On), (22 bytes).
    Removing oled.o(i.OLED_Pow), (22 bytes).
    Removing oled.o(i.OLED_ShowChar), (156 bytes).
    Removing oled.o(i.OLED_ShowFloat), (328 bytes).
    Removing oled.o(i.OLED_ShowHanzi), (100 bytes).
    Removing oled.o(i.OLED_ShowHzbig), (184 bytes).
    Removing oled.o(i.OLED_ShowNum), (136 bytes).
    Removing oled.o(i.OLED_ShowPic), (76 bytes).
    Removing oled.o(i.OLED_ShowStr), (58 bytes).
    Removing oled.o(.constdata), (2712 bytes).
    Removing mui.o(i.mui_Draw), (20 bytes).
    Removing mui.o(i.mui_EnterForm), (96 bytes).
    Removing mui.o(i.mui_GetCurrentCursorFocusPosition), (28 bytes).
    Removing mui.o(i.mui_GetCurrentFormId), (32 bytes).
    Removing mui.o(i.mui_GetSelectableFieldOptionCnt), (48 bytes).
    Removing mui.o(i.mui_GetSelectableFieldTextOption), (52 bytes).
    Removing mui.o(i.mui_GotoForm), (42 bytes).
    Removing mui.o(i.mui_GotoFormAutoCursorPosition), (44 bytes).
    Removing mui.o(i.mui_Init), (44 bytes).
    Removing mui.o(i.mui_LeaveForm), (52 bytes).
    Removing mui.o(i.mui_NextField), (46 bytes).
    Removing mui.o(i.mui_PrevField), (76 bytes).
    Removing mui.o(i.mui_RestoreForm), (58 bytes).
    Removing mui.o(i.mui_SaveCursorPosition), (92 bytes).
    Removing mui.o(i.mui_SaveForm), (22 bytes).
    Removing mui.o(i.mui_SaveFormWithCursorPosition), (114 bytes).
    Removing mui.o(i.mui_SendSelect), (14 bytes).
    Removing mui.o(i.mui_SendSelectWithExecuteOnSelectFieldSearch), (60 bytes).
    Removing mui.o(i.mui_SendValueDecrement), (14 bytes).
    Removing mui.o(i.mui_SendValueIncrement), (14 bytes).
    Removing mui.o(i.mui_fds_first_token), (44 bytes).
    Removing mui.o(i.mui_fds_get_cmd_size), (74 bytes).
    Removing mui.o(i.mui_fds_get_cmd_size_without_text), (120 bytes).
    Removing mui.o(i.mui_fds_get_nth_token), (40 bytes).
    Removing mui.o(i.mui_fds_get_token_cnt), (34 bytes).
    Removing mui.o(i.mui_fds_next_token), (86 bytes).
    Removing mui.o(i.mui_fds_parse_text), (92 bytes).
    Removing mui.o(i.mui_find_form), (62 bytes).
    Removing mui.o(i.mui_find_uif), (58 bytes).
    Removing mui.o(i.mui_get_fds_char), (6 bytes).
    Removing mui.o(i.mui_inner_loop_over_form), (68 bytes).
    Removing mui.o(i.mui_loop_over_form), (42 bytes).
    Removing mui.o(i.mui_next_field), (44 bytes).
    Removing mui.o(i.mui_prepare_current_field), (380 bytes).
    Removing mui.o(i.mui_send_cursor_enter_msg), (20 bytes).
    Removing mui.o(i.mui_send_cursor_msg), (38 bytes).
    Removing mui.o(i.mui_task_draw), (18 bytes).
    Removing mui.o(i.mui_task_find_execute_on_select_field), (24 bytes).
    Removing mui.o(i.mui_task_find_first_cursor_uif), (24 bytes).
    Removing mui.o(i.mui_task_find_last_cursor_uif), (20 bytes).
    Removing mui.o(i.mui_task_find_next_cursor_uif), (44 bytes).
    Removing mui.o(i.mui_task_find_prev_cursor_uif), (36 bytes).
    Removing mui.o(i.mui_task_form_end), (18 bytes).
    Removing mui.o(i.mui_task_form_start), (18 bytes).
    Removing mui.o(i.mui_task_get_current_cursor_focus_position), (38 bytes).
    Removing mui.o(i.mui_uif_is_cursor_selectable), (20 bytes).
    Removing mui_u8g2.o(i.mui_get_U8g2), (6 bytes).
    Removing mui_u8g2.o(i.mui_get_arg), (8 bytes).
    Removing mui_u8g2.o(i.mui_get_text), (8 bytes).
    Removing mui_u8g2.o(i.mui_get_x), (32 bytes).
    Removing mui_u8g2.o(i.mui_get_y), (8 bytes).
    Removing mui_u8g2.o(i.mui_hline), (52 bytes).
    Removing mui_u8g2.o(i.mui_is_valid_char), (50 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_back_w1_fi), (112 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_back_w1_pi), (108 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_back_w2_fi), (114 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_back_w2_if), (114 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_back_wm_fi), (90 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_back_wm_if), (90 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_exit_wm_fi), (110 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_goto_w1_fi), (122 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_goto_w1_pi), (118 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_goto_w2_fi), (124 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_goto_w2_if), (124 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_goto_wm_fi), (100 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_goto_wm_if), (100 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_draw_button_fi), (42 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_draw_button_if), (42 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_draw_button_pf), (42 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_draw_button_pi), (42 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_draw_button_utf), (72 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_draw_text), (92 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_get_fi_flags), (28 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_get_if_flags), (42 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_get_pf_flags), (30 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_get_pi_flags), (30 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_goto_data), (48 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf), (184 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi), (180 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_handle_scroll_next_prev_events), (136 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_s8_min_max_wm_mse_pf), (118 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_s8_min_max_wm_mse_pi), (118 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_s8_min_max_wm_mud_pf), (160 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_s8_min_max_wm_mud_pi), (160 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pf), (164 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pi), (168 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_set_font_style_function), (32 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u16_list_child_mse_common), (154 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u16_list_child_w1_pi), (204 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u16_list_goto_w1_pi), (200 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u16_list_line_wa_mse_pi), (138 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u16_list_line_wa_mud_pi), (174 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u16_list_parent_wm_pi), (108 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm), (388 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_bar_mse_msg_handler), (126 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_bar_mud_msg_handler), (166 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mse_pf), (46 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mse_pi), (46 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mud_pf), (46 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mud_pi), (46 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_char_wm_mud_pi), (192 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_chkbox_wm_pi), (292 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mse_pf), (46 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mse_pi), (46 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mud_pf), (46 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mud_pi), (46 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_min_max_wm_mse_pf), (110 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_min_max_wm_mse_pi), (110 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_min_max_wm_mud_pf), (152 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_min_max_wm_mud_pi), (152 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common), (138 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_opt_child_wm_pi), (128 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mse_pf), (138 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mse_pi), (138 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mud_pf), (174 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mud_pi), (174 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_opt_parent_wm_pi), (110 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi), (190 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi), (208 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_radio_wm_pi), (284 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pf), (132 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pi), (132 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_x8_min_max_wm_mse_pf), (110 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_x8_min_max_wm_mud_pf), (152 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_x8_min_max_wm_mud_pi), (152 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pf), (120 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pi), (120 bytes).
    Removing mui_u8g2.o(i.mui_x8g2_x8_min_max_wm_mse_pi), (110 bytes).
    Removing mui_u8g2.o(i.u8g2_DrawCheckbox), (66 bytes).
    Removing mui_u8g2.o(i.u8g2_DrawValueMark), (28 bytes).
    Removing mui_u8g2.o(.constdata), (6 bytes).
    Removing u8g2_arc.o(i.u8g2_DrawArc), (70 bytes).
    Removing u8g2_arc.o(i.u8g2_draw_arc), (600 bytes).
    Removing u8g2_bitmap.o(i.u8g2_DrawBitmap), (90 bytes).
    Removing u8g2_bitmap.o(i.u8g2_DrawHXBM), (232 bytes).
    Removing u8g2_bitmap.o(i.u8g2_DrawHXBMP), (232 bytes).
    Removing u8g2_bitmap.o(i.u8g2_DrawHorizontalBitmap), (152 bytes).
    Removing u8g2_bitmap.o(i.u8g2_DrawXBM), (88 bytes).
    Removing u8g2_bitmap.o(i.u8g2_DrawXBMP), (88 bytes).
    Removing u8g2_bitmap.o(i.u8g2_SetBitmapMode), (6 bytes).
    Removing u8g2_box.o(i.u8g2_DrawBox), (74 bytes).
    Removing u8g2_box.o(i.u8g2_DrawFrame), (138 bytes).
    Removing u8g2_box.o(i.u8g2_DrawRBox), (318 bytes).
    Removing u8g2_box.o(i.u8g2_DrawRFrame), (332 bytes).
    Removing u8g2_buffer.o(i.u8g2_FirstPage), (26 bytes).
    Removing u8g2_buffer.o(i.u8g2_NextPage), (64 bytes).
    Removing u8g2_buffer.o(i.u8g2_SetBufferCurrTileRow), (28 bytes).
    Removing u8g2_buffer.o(i.u8g2_UpdateDisplay), (12 bytes).
    Removing u8g2_buffer.o(i.u8g2_UpdateDisplayArea), (86 bytes).
    Removing u8g2_buffer.o(i.u8g2_WriteBufferPBM), (44 bytes).
    Removing u8g2_buffer.o(i.u8g2_WriteBufferPBM2), (44 bytes).
    Removing u8g2_buffer.o(i.u8g2_WriteBufferXBM), (44 bytes).
    Removing u8g2_buffer.o(i.u8g2_WriteBufferXBM2), (44 bytes).
    Removing u8g2_button.o(i.u8g2_DrawButtonFrame), (348 bytes).
    Removing u8g2_button.o(i.u8g2_DrawButtonUTF8), (124 bytes).
    Removing u8g2_circle.o(i.u8g2_DrawDisc), (70 bytes).
    Removing u8g2_circle.o(i.u8g2_DrawEllipse), (70 bytes).
    Removing u8g2_circle.o(i.u8g2_DrawFilledEllipse), (70 bytes).
    Removing u8g2_circle.o(i.u8g2_draw_disc), (128 bytes).
    Removing u8g2_circle.o(i.u8g2_draw_disc_section), (180 bytes).
    Removing u8g2_circle.o(i.u8g2_draw_ellipse), (294 bytes).
    Removing u8g2_circle.o(i.u8g2_draw_ellipse_section), (100 bytes).
    Removing u8g2_circle.o(i.u8g2_draw_filled_ellipse), (294 bytes).
    Removing u8g2_circle.o(i.u8g2_draw_filled_ellipse_section), (116 bytes).
    Removing u8g2_cleardisplay.o(i.u8g2_ClearDisplay), (32 bytes).
    Removing u8g2_font.o(i.u8g2_DrawExtUTF8), (180 bytes).
    Removing u8g2_font.o(i.u8g2_DrawExtendedUTF8), (188 bytes).
    Removing u8g2_font.o(i.u8g2_DrawGlyphX2), (42 bytes).
    Removing u8g2_font.o(i.u8g2_DrawHB), (78 bytes).
    Removing u8g2_font.o(i.u8g2_DrawStrX2), (36 bytes).
    Removing u8g2_font.o(i.u8g2_DrawUTF8), (36 bytes).
    Removing u8g2_font.o(i.u8g2_DrawUTF8X2), (36 bytes).
    Removing u8g2_font.o(i.u8g2_GetFontBBXHeight), (8 bytes).
    Removing u8g2_font.o(i.u8g2_GetFontBBXOffX), (8 bytes).
    Removing u8g2_font.o(i.u8g2_GetFontBBXOffY), (8 bytes).
    Removing u8g2_font.o(i.u8g2_GetFontBBXWidth), (8 bytes).
    Removing u8g2_font.o(i.u8g2_GetFontCapitalAHeight), (8 bytes).
    Removing u8g2_font.o(i.u8g2_GetFontSize), (70 bytes).
    Removing u8g2_font.o(i.u8g2_GetGlyphHorizontalProperties), (92 bytes).
    Removing u8g2_font.o(i.u8g2_GetGlyphWidth), (72 bytes).
    Removing u8g2_font.o(i.u8g2_GetStrWidth), (24 bytes).
    Removing u8g2_font.o(i.u8g2_GetStrX), (34 bytes).
    Removing u8g2_font.o(i.u8g2_GetUTF8Width), (24 bytes).
    Removing u8g2_font.o(i.u8g2_GetXOffsetGlyph), (20 bytes).
    Removing u8g2_font.o(i.u8g2_GetXOffsetUTF8), (62 bytes).
    Removing u8g2_font.o(i.u8g2_IsAllValidUTF8), (24 bytes).
    Removing u8g2_font.o(i.u8g2_IsGlyph), (24 bytes).
    Removing u8g2_font.o(i.u8g2_SetFont), (32 bytes).
    Removing u8g2_font.o(i.u8g2_SetFontDirection), (6 bytes).
    Removing u8g2_font.o(i.u8g2_SetFontMode), (6 bytes).
    Removing u8g2_font.o(i.u8g2_SetFontPosBottom), (12 bytes).
    Removing u8g2_font.o(i.u8g2_SetFontPosCenter), (12 bytes).
    Removing u8g2_font.o(i.u8g2_SetFontPosTop), (12 bytes).
    Removing u8g2_font.o(i.u8g2_SetFontRefHeightAll), (18 bytes).
    Removing u8g2_font.o(i.u8g2_SetFontRefHeightExtendedText), (18 bytes).
    Removing u8g2_font.o(i.u8g2_SetFontRefHeightText), (18 bytes).
    Removing u8g2_font.o(i.u8g2_UpdateRefHeight), (136 bytes).
    Removing u8g2_font.o(i.u8g2_draw_string_2x), (94 bytes).
    Removing u8g2_font.o(i.u8g2_font_2x_decode_glyph), (246 bytes).
    Removing u8g2_font.o(i.u8g2_font_2x_decode_len), (212 bytes).
    Removing u8g2_font.o(i.u8g2_font_2x_draw_glyph), (58 bytes).
    Removing u8g2_font.o(i.u8g2_font_calc_vref_bottom), (10 bytes).
    Removing u8g2_font.o(i.u8g2_font_calc_vref_center), (32 bytes).
    Removing u8g2_font.o(i.u8g2_font_calc_vref_top), (14 bytes).
    Removing u8g2_font.o(i.u8g2_font_get_byte), (8 bytes).
    Removing u8g2_font.o(i.u8g2_is_all_valid), (64 bytes).
    Removing u8g2_font.o(i.u8g2_read_font_info), (224 bytes).
    Removing u8g2_font.o(i.u8g2_string_width), (138 bytes).
    Removing u8g2_fonts.o(.constdata), (13637486 bytes).
    Removing u8g2_hvline.o(i.u8g2_DrawHLine), (28 bytes).
    Removing u8g2_hvline.o(i.u8g2_DrawVLine), (28 bytes).
    Removing u8g2_input_value.o(i.u8g2_UserInterfaceInputValue), (392 bytes).
    Removing u8g2_kerning.o(i.u8g2_GetKerning), (112 bytes).
    Removing u8g2_kerning.o(i.u8g2_GetKerningByTable), (68 bytes).
    Removing u8g2_line.o(i.u8g2_DrawLine), (214 bytes).
    Removing u8g2_ll_hvline.o(i.u8g2_ll_hvline_horizontal_right_lsb), (190 bytes).
    Removing u8g2_message.o(i.u8g2_UserInterfaceMessage), (368 bytes).
    Removing u8g2_message.o(i.u8g2_draw_button_line), (190 bytes).
    Removing u8g2_polygon.o(i.pg_AddPolygonXY), (28 bytes).
    Removing u8g2_polygon.o(i.pg_ClearPolygonXY), (6 bytes).
    Removing u8g2_polygon.o(i.pg_DrawPolygon), (28 bytes).
    Removing u8g2_polygon.o(i.pg_dec), (22 bytes).
    Removing u8g2_polygon.o(i.pg_exec), (110 bytes).
    Removing u8g2_polygon.o(i.pg_expand_min_y), (84 bytes).
    Removing u8g2_polygon.o(i.pg_hline), (164 bytes).
    Removing u8g2_polygon.o(i.pg_inc), (18 bytes).
    Removing u8g2_polygon.o(i.pg_line_init), (78 bytes).
    Removing u8g2_polygon.o(i.pg_prepare), (192 bytes).
    Removing u8g2_polygon.o(i.pge_Init), (90 bytes).
    Removing u8g2_polygon.o(i.pge_Next), (78 bytes).
    Removing u8g2_polygon.o(i.u8g2_AddPolygonXY), (24 bytes).
    Removing u8g2_polygon.o(i.u8g2_ClearPolygonXY), (16 bytes).
    Removing u8g2_polygon.o(i.u8g2_DrawPolygon), (20 bytes).
    Removing u8g2_polygon.o(i.u8g2_DrawTriangle), (64 bytes).
    Removing u8g2_polygon.o(.bss), (76 bytes).
    Removing u8g2_selection_list.o(i.u8g2_DrawSelectionList), (52 bytes).
    Removing u8g2_selection_list.o(i.u8g2_DrawUTF8Line), (252 bytes).
    Removing u8g2_selection_list.o(i.u8g2_DrawUTF8Lines), (88 bytes).
    Removing u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList), (352 bytes).
    Removing u8g2_selection_list.o(i.u8g2_draw_selection_list_line), (96 bytes).
    Removing u8g2_setup.o(i.u8g2_SendF), (30 bytes).
    Removing u8g2_setup.o(i.u8g2_SetClipWindow), (42 bytes).
    Removing u8g2_setup.o(i.u8g2_SetDisplayRotation), (26 bytes).
    Removing u8g2_setup.o(i.u8g2_Setup_null), (56 bytes).
    Removing u8g2_setup.o(.data), (8 bytes).
    Removing u8log.o(i.u8log_Init), (36 bytes).
    Removing u8log.o(i.u8log_SetCallback), (6 bytes).
    Removing u8log.o(i.u8log_SetLineHeightOffset), (4 bytes).
    Removing u8log.o(i.u8log_SetRedrawMode), (4 bytes).
    Removing u8log.o(i.u8log_WriteChar), (40 bytes).
    Removing u8log.o(i.u8log_WriteDec16), (32 bytes).
    Removing u8log.o(i.u8log_WriteDec8), (32 bytes).
    Removing u8log.o(i.u8log_WriteHex16), (24 bytes).
    Removing u8log.o(i.u8log_WriteHex32), (24 bytes).
    Removing u8log.o(i.u8log_WriteHex8), (24 bytes).
    Removing u8log.o(i.u8log_WriteHexHalfByte), (42 bytes).
    Removing u8log.o(i.u8log_WriteString), (26 bytes).
    Removing u8log.o(i.u8log_clear_screen), (26 bytes).
    Removing u8log.o(i.u8log_cursor_on_screen), (46 bytes).
    Removing u8log.o(i.u8log_scroll_up), (72 bytes).
    Removing u8log.o(i.u8log_write_char), (120 bytes).
    Removing u8log.o(i.u8log_write_to_screen), (46 bytes).
    Removing u8log_u8g2.o(i.u8g2_DrawLog), (114 bytes).
    Removing u8log_u8g2.o(i.u8log_u8g2_cb), (50 bytes).
    Removing u8log_u8x8.o(i.u8log_u8x8_cb), (44 bytes).
    Removing u8log_u8x8.o(i.u8x8_DrawLog), (48 bytes).
    Removing u8log_u8x8.o(i.u8x8_DrawLogLine), (60 bytes).
    Removing u8x8_8x8.o(i.u8x8_Draw1x2Glyph), (96 bytes).
    Removing u8x8_8x8.o(i.u8x8_Draw1x2String), (36 bytes).
    Removing u8x8_8x8.o(i.u8x8_Draw1x2UTF8), (36 bytes).
    Removing u8x8_8x8.o(i.u8x8_Draw2x2Glyph), (104 bytes).
    Removing u8x8_8x8.o(i.u8x8_Draw2x2String), (36 bytes).
    Removing u8x8_8x8.o(i.u8x8_Draw2x2UTF8), (36 bytes).
    Removing u8x8_8x8.o(i.u8x8_DrawGlyph), (106 bytes).
    Removing u8x8_8x8.o(i.u8x8_DrawString), (36 bytes).
    Removing u8x8_8x8.o(i.u8x8_DrawUTF8), (36 bytes).
    Removing u8x8_8x8.o(i.u8x8_GetUTF8Len), (58 bytes).
    Removing u8x8_8x8.o(i.u8x8_SetFont), (4 bytes).
    Removing u8x8_8x8.o(i.u8x8_draw_1x2_string), (96 bytes).
    Removing u8x8_8x8.o(i.u8x8_draw_1x2_subglyph), (102 bytes).
    Removing u8x8_8x8.o(i.u8x8_draw_2x2_string), (104 bytes).
    Removing u8x8_8x8.o(i.u8x8_draw_2x2_subglyph), (172 bytes).
    Removing u8x8_8x8.o(i.u8x8_draw_string), (96 bytes).
    Removing u8x8_8x8.o(i.u8x8_get_glyph_data), (202 bytes).
    Removing u8x8_8x8.o(i.u8x8_upscale_buf), (28 bytes).
    Removing u8x8_8x8.o(i.u8x8_upscale_byte), (42 bytes).
    Removing u8x8_8x8.o(i.u8x8_utf8_next), (156 bytes).
    Removing u8x8_byte.o(i.i2c_clear_scl), (16 bytes).
    Removing u8x8_byte.o(i.i2c_clear_sda), (16 bytes).
    Removing u8x8_byte.o(i.i2c_delay), (18 bytes).
    Removing u8x8_byte.o(i.i2c_init), (32 bytes).
    Removing u8x8_byte.o(i.i2c_read_bit), (42 bytes).
    Removing u8x8_byte.o(i.i2c_read_scl_and_delay), (22 bytes).
    Removing u8x8_byte.o(i.i2c_read_sda), (16 bytes).
    Removing u8x8_byte.o(i.i2c_start), (60 bytes).
    Removing u8x8_byte.o(i.i2c_stop), (42 bytes).
    Removing u8x8_byte.o(i.i2c_write_bit), (42 bytes).
    Removing u8x8_byte.o(i.i2c_write_byte), (94 bytes).
    Removing u8x8_byte.o(i.u8x8_byte_3wire_sw_spi), (304 bytes).
    Removing u8x8_byte.o(i.u8x8_byte_4wire_sw_spi), (288 bytes).
    Removing u8x8_byte.o(i.u8x8_byte_8bit_6800mode), (248 bytes).
    Removing u8x8_byte.o(i.u8x8_byte_8bit_8080mode), (248 bytes).
    Removing u8x8_byte.o(i.u8x8_byte_SetDC), (20 bytes).
    Removing u8x8_byte.o(i.u8x8_byte_empty), (46 bytes).
    Removing u8x8_byte.o(i.u8x8_byte_ks0108), (240 bytes).
    Removing u8x8_byte.o(i.u8x8_byte_sed1520), (240 bytes).
    Removing u8x8_byte.o(i.u8x8_byte_set_ks0108_cs), (48 bytes).
    Removing u8x8_byte.o(i.u8x8_byte_sw_i2c), (110 bytes).
    Removing u8x8_byte.o(.data), (2 bytes).
    Removing u8x8_cad.o(i.u8x8_SendF), (30 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_001), (108 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_011), (108 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_011_ssd13xx_i2c), (174 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_100), (108 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_110), (108 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_SendMultipleArg), (38 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_empty), (84 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_ld7032_i2c), (208 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_ssd13xx_i2c), (146 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_st75256_i2c), (174 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_st7920_spi), (288 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_uc1638_i2c), (396 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_uc16xx_i2c), (308 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_vsendf), (100 bytes).
    Removing u8x8_cad.o(i.u8x8_gu800_cad_110), (166 bytes).
    Removing u8x8_cad.o(.bss), (16 bytes).
    Removing u8x8_capture.o(i.u8x8_capture_get_pixel_1), (44 bytes).
    Removing u8x8_capture.o(i.u8x8_capture_get_pixel_2), (38 bytes).
    Removing u8x8_capture.o(i.u8x8_capture_write_pbm_buffer), (100 bytes).
    Removing u8x8_capture.o(i.u8x8_capture_write_pbm_pre), (56 bytes).
    Removing u8x8_capture.o(i.u8x8_capture_write_xbm_buffer), (268 bytes).
    Removing u8x8_capture.o(i.u8x8_capture_write_xbm_pre), (140 bytes).
    Removing u8x8_debounce.o(i.u8x8_GetMenuEvent), (150 bytes).
    Removing u8x8_debounce.o(i.u8x8_find_first_diff), (44 bytes).
    Removing u8x8_debounce.o(i.u8x8_read_pin_state), (56 bytes).
    Removing u8x8_display.o(i.u8x8_ClearDisplay), (20 bytes).
    Removing u8x8_display.o(i.u8x8_ClearDisplayWithTile), (72 bytes).
    Removing u8x8_display.o(i.u8x8_ClearLine), (56 bytes).
    Removing u8x8_display.o(i.u8x8_FillDisplay), (32 bytes).
    Removing u8x8_display.o(i.u8x8_InitInterface), (30 bytes).
    Removing u8x8_display.o(i.u8x8_SetContrast), (20 bytes).
    Removing u8x8_display.o(i.u8x8_SetFlipMode), (20 bytes).
    Removing u8x8_fonts.o(.constdata), (487478 bytes).
    Removing u8x8_input_value.o(i.u8x8_UserInterfaceInputValue), (372 bytes).
    Removing u8x8_message.o(i.u8x8_UserInterfaceMessage), (298 bytes).
    Removing u8x8_message.o(i.u8x8_draw_button_line), (188 bytes).
    Removing u8x8_selection_list.o(i.u8sl_Next), (48 bytes).
    Removing u8x8_selection_list.o(i.u8sl_Prev), (52 bytes).
    Removing u8x8_selection_list.o(i.u8x8_DrawSelectionList), (44 bytes).
    Removing u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList), (244 bytes).
    Removing u8x8_selection_list.o(i.u8x8_sl_string_line_cb), (100 bytes).
    Removing u8x8_setup.o(i.u8x8_d_null_cb), (52 bytes).
    Removing u8x8_setup.o(.constdata), (24 bytes).
    Removing u8x8_string.o(i.u8x8_CopyStringLine), (58 bytes).
    Removing u8x8_string.o(i.u8x8_DrawUTF8Line), (136 bytes).
    Removing u8x8_string.o(i.u8x8_DrawUTF8Lines), (68 bytes).
    Removing u8x8_string.o(i.u8x8_GetStringLineCnt), (36 bytes).
    Removing u8x8_string.o(i.u8x8_GetStringLineStart), (46 bytes).
    Removing u8x8_u8toa.o(i.u8x8_s8toa), (84 bytes).
    Removing u8x8_u8toa.o(i.u8x8_u8toa), (28 bytes).
    Removing u8x8_u8toa.o(i.u8x8_u8toap), (48 bytes).
    Removing u8x8_u8toa.o(i.u8x8_u8tox), (100 bytes).
    Removing u8x8_u8toa.o(.constdata), (3 bytes).
    Removing u8x8_u8toa.o(.data), (12 bytes).
    Removing u8x8_u16toa.o(i.u8x8_u16toa), (28 bytes).
    Removing u8x8_u16toa.o(i.u8x8_u16toap), (50 bytes).
    Removing u8x8_u16toa.o(i.u8x8_utoa), (34 bytes).
    Removing u8x8_u16toa.o(.data), (6 bytes).
    Removing fadd.o(.text), (176 bytes).
    Removing fmul.o(.text), (100 bytes).
    Removing ffltui.o(.text), (10 bytes).
    Removing ffixui.o(.text), (40 bytes).
    Removing cfcmple.o(.text), (20 bytes).
    Removing fepilogue.o(.text), (110 bytes).

692 unused section(s) (total 14189853 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\APP\btn_app.c                         0x00000000   Number         0  btn_app.o ABSOLUTE
    ..\APP\key_app.c                         0x00000000   Number         0  key_app.o ABSOLUTE
    ..\APP\led_app.c                         0x00000000   Number         0  led_app.o ABSOLUTE
    ..\APP\oled_app.c                        0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\APP\scheduler.c                       0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\APP\usart_app.c                       0x00000000   Number         0  usart_app.o ABSOLUTE
    ..\Components\ebtn\ebtn.c                0x00000000   Number         0  ebtn.o ABSOLUTE
    ..\Components\oled\oled.c                0x00000000   Number         0  oled.o ABSOLUTE
    ..\Components\ringbuffer\ringbuffer.c    0x00000000   Number         0  ringbuffer.o ABSOLUTE
    ..\Components\u8g2\mui.c                 0x00000000   Number         0  mui.o ABSOLUTE
    ..\Components\u8g2\mui_u8g2.c            0x00000000   Number         0  mui_u8g2.o ABSOLUTE
    ..\Components\u8g2\u8g2_arc.c            0x00000000   Number         0  u8g2_arc.o ABSOLUTE
    ..\Components\u8g2\u8g2_bitmap.c         0x00000000   Number         0  u8g2_bitmap.o ABSOLUTE
    ..\Components\u8g2\u8g2_box.c            0x00000000   Number         0  u8g2_box.o ABSOLUTE
    ..\Components\u8g2\u8g2_buffer.c         0x00000000   Number         0  u8g2_buffer.o ABSOLUTE
    ..\Components\u8g2\u8g2_button.c         0x00000000   Number         0  u8g2_button.o ABSOLUTE
    ..\Components\u8g2\u8g2_circle.c         0x00000000   Number         0  u8g2_circle.o ABSOLUTE
    ..\Components\u8g2\u8g2_cleardisplay.c   0x00000000   Number         0  u8g2_cleardisplay.o ABSOLUTE
    ..\Components\u8g2\u8g2_d_memory.c       0x00000000   Number         0  u8g2_d_memory.o ABSOLUTE
    ..\Components\u8g2\u8g2_d_setup.c        0x00000000   Number         0  u8g2_d_setup.o ABSOLUTE
    ..\Components\u8g2\u8g2_font.c           0x00000000   Number         0  u8g2_font.o ABSOLUTE
    ..\Components\u8g2\u8g2_fonts.c          0x00000000   Number         0  u8g2_fonts.o ABSOLUTE
    ..\Components\u8g2\u8g2_hvline.c         0x00000000   Number         0  u8g2_hvline.o ABSOLUTE
    ..\Components\u8g2\u8g2_input_value.c    0x00000000   Number         0  u8g2_input_value.o ABSOLUTE
    ..\Components\u8g2\u8g2_intersection.c   0x00000000   Number         0  u8g2_intersection.o ABSOLUTE
    ..\Components\u8g2\u8g2_kerning.c        0x00000000   Number         0  u8g2_kerning.o ABSOLUTE
    ..\Components\u8g2\u8g2_line.c           0x00000000   Number         0  u8g2_line.o ABSOLUTE
    ..\Components\u8g2\u8g2_ll_hvline.c      0x00000000   Number         0  u8g2_ll_hvline.o ABSOLUTE
    ..\Components\u8g2\u8g2_message.c        0x00000000   Number         0  u8g2_message.o ABSOLUTE
    ..\Components\u8g2\u8g2_polygon.c        0x00000000   Number         0  u8g2_polygon.o ABSOLUTE
    ..\Components\u8g2\u8g2_selection_list.c 0x00000000   Number         0  u8g2_selection_list.o ABSOLUTE
    ..\Components\u8g2\u8g2_setup.c          0x00000000   Number         0  u8g2_setup.o ABSOLUTE
    ..\Components\u8g2\u8log.c               0x00000000   Number         0  u8log.o ABSOLUTE
    ..\Components\u8g2\u8log_u8g2.c          0x00000000   Number         0  u8log_u8g2.o ABSOLUTE
    ..\Components\u8g2\u8log_u8x8.c          0x00000000   Number         0  u8log_u8x8.o ABSOLUTE
    ..\Components\u8g2\u8x8_8x8.c            0x00000000   Number         0  u8x8_8x8.o ABSOLUTE
    ..\Components\u8g2\u8x8_byte.c           0x00000000   Number         0  u8x8_byte.o ABSOLUTE
    ..\Components\u8g2\u8x8_cad.c            0x00000000   Number         0  u8x8_cad.o ABSOLUTE
    ..\Components\u8g2\u8x8_capture.c        0x00000000   Number         0  u8x8_capture.o ABSOLUTE
    ..\Components\u8g2\u8x8_d_ssd1315_128x64_noname.c 0x00000000   Number         0  u8x8_d_ssd1315_128x64_noname.o ABSOLUTE
    ..\Components\u8g2\u8x8_debounce.c       0x00000000   Number         0  u8x8_debounce.o ABSOLUTE
    ..\Components\u8g2\u8x8_display.c        0x00000000   Number         0  u8x8_display.o ABSOLUTE
    ..\Components\u8g2\u8x8_fonts.c          0x00000000   Number         0  u8x8_fonts.o ABSOLUTE
    ..\Components\u8g2\u8x8_gpio.c           0x00000000   Number         0  u8x8_gpio.o ABSOLUTE
    ..\Components\u8g2\u8x8_input_value.c    0x00000000   Number         0  u8x8_input_value.o ABSOLUTE
    ..\Components\u8g2\u8x8_message.c        0x00000000   Number         0  u8x8_message.o ABSOLUTE
    ..\Components\u8g2\u8x8_selection_list.c 0x00000000   Number         0  u8x8_selection_list.o ABSOLUTE
    ..\Components\u8g2\u8x8_setup.c          0x00000000   Number         0  u8x8_setup.o ABSOLUTE
    ..\Components\u8g2\u8x8_string.c         0x00000000   Number         0  u8x8_string.o ABSOLUTE
    ..\Components\u8g2\u8x8_u16toa.c         0x00000000   Number         0  u8x8_u16toa.o ABSOLUTE
    ..\Components\u8g2\u8x8_u8toa.c          0x00000000   Number         0  u8x8_u8toa.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\\APP\\btn_app.c                       0x00000000   Number         0  btn_app.o ABSOLUTE
    ..\\APP\\key_app.c                       0x00000000   Number         0  key_app.o ABSOLUTE
    ..\\APP\\led_app.c                       0x00000000   Number         0  led_app.o ABSOLUTE
    ..\\APP\\oled_app.c                      0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\\APP\\scheduler.c                     0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\\APP\\usart_app.c                     0x00000000   Number         0  usart_app.o ABSOLUTE
    ..\\Components\\oled\\oled.c             0x00000000   Number         0  oled.o ABSOLUTE
    D:/CubeMax/Repository/STM32Cube_FW_F1_V1.8.6/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    D:/CubeMax/Repository/STM32Cube_FW_F1_V1.8.6/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    D:/CubeMax/Repository/STM32Cube_FW_F1_V1.8.6/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    D:/CubeMax/Repository/STM32Cube_FW_F1_V1.8.6/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    D:/CubeMax/Repository/STM32Cube_FW_F1_V1.8.6/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    D:/CubeMax/Repository/STM32Cube_FW_F1_V1.8.6/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    D:/CubeMax/Repository/STM32Cube_FW_F1_V1.8.6/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    D:/CubeMax/Repository/STM32Cube_FW_F1_V1.8.6/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    D:/CubeMax/Repository/STM32Cube_FW_F1_V1.8.6/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_i2c.c 0x00000000   Number         0  stm32f1xx_hal_i2c.o ABSOLUTE
    D:/CubeMax/Repository/STM32Cube_FW_F1_V1.8.6/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    D:/CubeMax/Repository/STM32Cube_FW_F1_V1.8.6/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    D:/CubeMax/Repository/STM32Cube_FW_F1_V1.8.6/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    D:/CubeMax/Repository/STM32Cube_FW_F1_V1.8.6/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    D:\CubeMax\Repository\STM32Cube_FW_F1_V1.8.6\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    D:\CubeMax\Repository\STM32Cube_FW_F1_V1.8.6\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    D:\CubeMax\Repository\STM32Cube_FW_F1_V1.8.6\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    D:\CubeMax\Repository\STM32Cube_FW_F1_V1.8.6\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    D:\CubeMax\Repository\STM32Cube_FW_F1_V1.8.6\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    D:\CubeMax\Repository\STM32Cube_FW_F1_V1.8.6\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    D:\CubeMax\Repository\STM32Cube_FW_F1_V1.8.6\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    D:\CubeMax\Repository\STM32Cube_FW_F1_V1.8.6\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    D:\CubeMax\Repository\STM32Cube_FW_F1_V1.8.6\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_i2c.c 0x00000000   Number         0  stm32f1xx_hal_i2c.o ABSOLUTE
    D:\CubeMax\Repository\STM32Cube_FW_F1_V1.8.6\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    D:\CubeMax\Repository\STM32Cube_FW_F1_V1.8.6\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    D:\CubeMax\Repository\STM32Cube_FW_F1_V1.8.6\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    D:\CubeMax\Repository\STM32Cube_FW_F1_V1.8.6\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    cfcmple.s                                0x00000000   Number         0  cfcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32f103xe.s                    0x00000000   Number         0  startup_stm32f103xe.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f103xe.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000130   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000130   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000134   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000138   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000138   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000138   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x08000140   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x08000144   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x08000144   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x08000144   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000144   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000148   Section       36  startup_stm32f103xe.o(.text)
    .text                                    0x0800016c   Section        0  llushr.o(.text)
    .text                                    0x0800018c   Section        0  memcpya.o(.text)
    .text                                    0x080001b0   Section        0  memseta.o(.text)
    .text                                    0x080001d4   Section        0  memcmp.o(.text)
    .text                                    0x080001ee   Section        0  uidiv.o(.text)
    .text                                    0x0800021a   Section        0  uldiv.o(.text)
    .text                                    0x0800027c   Section        0  iusefp.o(.text)
    .text                                    0x0800027c   Section        0  dadd.o(.text)
    .text                                    0x080003ca   Section        0  dmul.o(.text)
    .text                                    0x080004ae   Section        0  ddiv.o(.text)
    .text                                    0x0800058c   Section        0  dfixul.o(.text)
    .text                                    0x080005bc   Section       48  cdrcmple.o(.text)
    .text                                    0x080005ec   Section       36  init.o(.text)
    .text                                    0x08000610   Section        0  llshl.o(.text)
    .text                                    0x0800062e   Section        0  llsshr.o(.text)
    .text                                    0x08000652   Section        0  depilogue.o(.text)
    i.BusFault_Handler                       0x0800070c   Section        0  stm32f1xx_it.o(i.BusFault_Handler)
    i.DMA1_Channel5_IRQHandler               0x08000710   Section        0  stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler)
    i.DMA_SetConfig                          0x08000720   Section        0  stm32f1xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08000721   Thumb Code    44  stm32f1xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x0800074c   Section        0  stm32f1xx_it.o(i.DebugMon_Handler)
    i.Error_Handler                          0x0800074e   Section        0  main.o(i.Error_Handler)
    i.HAL_DMA_Abort                          0x08000754   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x080007ac   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_GetState                       0x08000954   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_GetState)
    i.HAL_DMA_IRQHandler                     0x0800095c   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08000cf4   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x08000d80   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x08000e1c   Section        0  stm32f1xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x08000e44   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x08001184   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x08001194   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x080011a0   Section        0  stm32f1xx_hal.o(i.HAL_GetTick)
    i.HAL_I2C_AbortCpltCallback              0x080011ac   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback)
    i.HAL_I2C_AddrCallback                   0x080011ae   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_AddrCallback)
    i.HAL_I2C_EV_IRQHandler                  0x080011b0   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler)
    i.HAL_I2C_ErrorCallback                  0x0800138a   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback)
    i.HAL_I2C_Init                           0x0800138c   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_ListenCpltCallback             0x0800155c   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback)
    i.HAL_I2C_MasterRxCpltCallback           0x0800155e   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback)
    i.HAL_I2C_MasterTxCpltCallback           0x08001560   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback)
    i.HAL_I2C_Master_Transmit                0x08001564   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    i.HAL_I2C_MemRxCpltCallback              0x080016d4   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback)
    i.HAL_I2C_MemTxCpltCallback              0x080016d6   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback)
    i.HAL_I2C_Mem_Write                      0x080016d8   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    i.HAL_I2C_MspInit                        0x0800183c   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_I2C_SlaveRxCpltCallback            0x080018c0   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback)
    i.HAL_I2C_SlaveTxCpltCallback            0x080018c2   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback)
    i.HAL_IncTick                            0x080018c4   Section        0  stm32f1xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x080018dc   Section        0  stm32f1xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08001904   Section        0  stm32f1xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08001950   Section        0  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x080019a4   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x080019c4   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08001a40   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08001a68   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x08001bf4   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x08001c00   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08001c20   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08001c40   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08001cb0   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x08002114   Section        0  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x08002148   Section        0  stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x080021b8   Section        0  usart_app.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_DMAStop                       0x08002210   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop)
    i.HAL_UART_ErrorCallback                 0x0800229a   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x0800229c   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08002580   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x080025f8   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_RxCpltCallback                0x080026c8   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x080026ca   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x080026cc   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x0800278a   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x0800278c   Section        0  stm32f1xx_it.o(i.HardFault_Handler)
    i.I2C1_EV_IRQHandler                     0x08002790   Section        0  stm32f1xx_it.o(i.I2C1_EV_IRQHandler)
    i.I2C_ConvertOtherXferOptions            0x080027a0   Section        0  stm32f1xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions)
    I2C_ConvertOtherXferOptions              0x080027a1   Thumb Code    28  stm32f1xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions)
    i.I2C_DMAAbort                           0x080027bc   Section        0  stm32f1xx_hal_i2c.o(i.I2C_DMAAbort)
    I2C_DMAAbort                             0x080027bd   Thumb Code   242  stm32f1xx_hal_i2c.o(i.I2C_DMAAbort)
    i.I2C_Flush_DR                           0x080028b4   Section        0  stm32f1xx_hal_i2c.o(i.I2C_Flush_DR)
    I2C_Flush_DR                             0x080028b5   Thumb Code    18  stm32f1xx_hal_i2c.o(i.I2C_Flush_DR)
    i.I2C_ITError                            0x080028c8   Section        0  stm32f1xx_hal_i2c.o(i.I2C_ITError)
    I2C_ITError                              0x080028c9   Thumb Code   420  stm32f1xx_hal_i2c.o(i.I2C_ITError)
    i.I2C_IsAcknowledgeFailed                0x08002a74   Section        0  stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    I2C_IsAcknowledgeFailed                  0x08002a75   Thumb Code    62  stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    i.I2C_MasterReceive_BTF                  0x08002ab2   Section        0  stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF)
    I2C_MasterReceive_BTF                    0x08002ab3   Thumb Code   304  stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF)
    i.I2C_MasterReceive_RXNE                 0x08002be2   Section        0  stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE)
    I2C_MasterReceive_RXNE                   0x08002be3   Thumb Code   260  stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE)
    i.I2C_MasterRequestWrite                 0x08002ce8   Section        0  stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite)
    I2C_MasterRequestWrite                   0x08002ce9   Thumb Code   186  stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite)
    i.I2C_MasterTransmit_BTF                 0x08002da8   Section        0  stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF)
    I2C_MasterTransmit_BTF                   0x08002da9   Thumb Code   160  stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF)
    i.I2C_MasterTransmit_TXE                 0x08002e48   Section        0  stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE)
    I2C_MasterTransmit_TXE                   0x08002e49   Thumb Code   216  stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE)
    i.I2C_Master_ADD10                       0x08002f20   Section        0  stm32f1xx_hal_i2c.o(i.I2C_Master_ADD10)
    I2C_Master_ADD10                         0x08002f21   Thumb Code    42  stm32f1xx_hal_i2c.o(i.I2C_Master_ADD10)
    i.I2C_Master_ADDR                        0x08002f4a   Section        0  stm32f1xx_hal_i2c.o(i.I2C_Master_ADDR)
    I2C_Master_ADDR                          0x08002f4b   Thumb Code   640  stm32f1xx_hal_i2c.o(i.I2C_Master_ADDR)
    i.I2C_Master_SB                          0x080031ca   Section        0  stm32f1xx_hal_i2c.o(i.I2C_Master_SB)
    I2C_Master_SB                            0x080031cb   Thumb Code   158  stm32f1xx_hal_i2c.o(i.I2C_Master_SB)
    i.I2C_MemoryTransmit_TXE_BTF             0x08003268   Section        0  stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF)
    I2C_MemoryTransmit_TXE_BTF               0x08003269   Thumb Code   198  stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF)
    i.I2C_RequestMemoryWrite                 0x08003330   Section        0  stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    I2C_RequestMemoryWrite                   0x08003331   Thumb Code   220  stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    i.I2C_SlaveReceive_BTF                   0x08003410   Section        0  stm32f1xx_hal_i2c.o(i.I2C_SlaveReceive_BTF)
    I2C_SlaveReceive_BTF                     0x08003411   Thumb Code    26  stm32f1xx_hal_i2c.o(i.I2C_SlaveReceive_BTF)
    i.I2C_SlaveReceive_RXNE                  0x0800342a   Section        0  stm32f1xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE)
    I2C_SlaveReceive_RXNE                    0x0800342b   Thumb Code    70  stm32f1xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE)
    i.I2C_SlaveTransmit_BTF                  0x08003470   Section        0  stm32f1xx_hal_i2c.o(i.I2C_SlaveTransmit_BTF)
    I2C_SlaveTransmit_BTF                    0x08003471   Thumb Code    26  stm32f1xx_hal_i2c.o(i.I2C_SlaveTransmit_BTF)
    i.I2C_SlaveTransmit_TXE                  0x0800348a   Section        0  stm32f1xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE)
    I2C_SlaveTransmit_TXE                    0x0800348b   Thumb Code    70  stm32f1xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE)
    i.I2C_Slave_ADDR                         0x080034d0   Section        0  stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR)
    I2C_Slave_ADDR                           0x080034d1   Thumb Code   112  stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR)
    i.I2C_Slave_STOPF                        0x08003540   Section        0  stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF)
    I2C_Slave_STOPF                          0x08003541   Thumb Code   386  stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF)
    i.I2C_WaitOnBTFFlagUntilTimeout          0x080036cc   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    I2C_WaitOnBTFFlagUntilTimeout            0x080036cd   Thumb Code   102  stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    i.I2C_WaitOnFlagUntilTimeout             0x08003732   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x08003733   Thumb Code   190  stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnMasterAddressFlagUntilTimeout 0x080037f0   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    I2C_WaitOnMasterAddressFlagUntilTimeout  0x080037f1   Thumb Code   250  stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    i.I2C_WaitOnSTOPRequestThroughIT         0x080038ec   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnSTOPRequestThroughIT)
    I2C_WaitOnSTOPRequestThroughIT           0x080038ed   Thumb Code    74  stm32f1xx_hal_i2c.o(i.I2C_WaitOnSTOPRequestThroughIT)
    i.I2C_WaitOnTXEFlagUntilTimeout          0x0800393c   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    I2C_WaitOnTXEFlagUntilTimeout            0x0800393d   Thumb Code   102  stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    i.MX_DMA_Init                            0x080039a4   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x080039d8   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x08003aa0   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MX_USART1_UART_Init                    0x08003adc   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MemManage_Handler                      0x08003b34   Section        0  stm32f1xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08003b38   Section        0  stm32f1xx_it.o(i.NMI_Handler)
    i.OLED_Clear                             0x08003b3c   Section        0  oled.o(i.OLED_Clear)
    i.OLED_Init                              0x08003b74   Section        0  oled.o(i.OLED_Init)
    i.OLED_Set_Position                      0x08003ba4   Section        0  oled.o(i.OLED_Set_Position)
    i.OLED_Write_cmd                         0x08003bc8   Section        0  oled.o(i.OLED_Write_cmd)
    i.OLED_Write_data                        0x08003bf0   Section        0  oled.o(i.OLED_Write_data)
    i.PendSV_Handler                         0x08003c18   Section        0  stm32f1xx_it.o(i.PendSV_Handler)
    i.RCC_Delay                              0x08003c1c   Section        0  stm32f1xx_hal_rcc.o(i.RCC_Delay)
    RCC_Delay                                0x08003c1d   Thumb Code    36  stm32f1xx_hal_rcc.o(i.RCC_Delay)
    i.SVC_Handler                            0x08003c44   Section        0  stm32f1xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08003c46   Section        0  stm32f1xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08003c4e   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08003cb4   Section        0  system_stm32f1xx.o(i.SystemInit)
    i.UART_DMAAbortOnError                   0x08003cb6   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08003cb7   Thumb Code    20  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x08003cca   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x08003ccb   Thumb Code    80  stm32f1xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x08003d1a   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x08003d1b   Thumb Code   180  stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x08003dce   Section        0  stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x08003dcf   Thumb Code    36  stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x08003df2   Section        0  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08003df3   Thumb Code   108  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTransmit_IT                    0x08003e5e   Section        0  stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x08003e5f   Thumb Code    32  stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_EndTxTransfer                     0x08003e7e   Section        0  stm32f1xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x08003e7f   Thumb Code    38  stm32f1xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x08003ea4   Section        0  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08003ea5   Thumb Code   252  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08003fa0   Section        0  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08003fa1   Thumb Code   248  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x0800409c   Section        0  stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_Transmit_IT                       0x08004164   Section        0  stm32f1xx_hal_uart.o(i.UART_Transmit_IT)
    UART_Transmit_IT                         0x08004165   Thumb Code    96  stm32f1xx_hal_uart.o(i.UART_Transmit_IT)
    i.UART_WaitOnFlagUntilTimeout            0x080041c4   Section        0  stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x080041c5   Thumb Code   140  stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08004250   Section        0  stm32f1xx_it.o(i.USART1_IRQHandler)
    i.UsageFault_Handler                     0x08004260   Section        0  stm32f1xx_it.o(i.UsageFault_Handler)
    i.__0vsnprintf                           0x08004264   Section        0  printfa.o(i.__0vsnprintf)
    i.__NVIC_GetPriorityGrouping             0x08004298   Section        0  stm32f1xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    __NVIC_GetPriorityGrouping               0x08004299   Thumb Code    10  stm32f1xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    i.__NVIC_SetPriority                     0x080042a8   Section        0  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x080042a9   Thumb Code    32  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__aeabi_assert                         0x080042d0   Section        0  usart_app.o(i.__aeabi_assert)
    i.__scatterload_copy                     0x08004318   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08004326   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08004328   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x08004338   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x08004339   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x080044bc   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x080044bd   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08004b70   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08004b71   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08004b94   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08004b95   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._snputc                                0x08004bc2   Section        0  printfa.o(i._snputc)
    _snputc                                  0x08004bc3   Thumb Code    22  printfa.o(i._snputc)
    i.app_ebtn_init                          0x08004bd8   Section        0  btn_app.o(i.app_ebtn_init)
    i.bit_array_and                          0x08004c38   Section        0  ebtn.o(i.bit_array_and)
    bit_array_and                            0x08004c39   Thumb Code    38  ebtn.o(i.bit_array_and)
    i.bit_array_assign                       0x08004c5e   Section        0  ebtn.o(i.bit_array_assign)
    bit_array_assign                         0x08004c5f   Thumb Code    46  ebtn.o(i.bit_array_assign)
    i.bit_array_cmp                          0x08004c8c   Section        0  ebtn.o(i.bit_array_cmp)
    bit_array_cmp                            0x08004c8d   Thumb Code    36  ebtn.o(i.bit_array_cmp)
    i.bit_array_get                          0x08004cb0   Section        0  ebtn.o(i.bit_array_get)
    bit_array_get                            0x08004cb1   Thumb Code    22  ebtn.o(i.bit_array_get)
    i.bit_array_num_bits_set                 0x08004cc6   Section        0  ebtn.o(i.bit_array_num_bits_set)
    bit_array_num_bits_set                   0x08004cc7   Thumb Code    84  ebtn.o(i.bit_array_num_bits_set)
    i.bit_array_or                           0x08004d1a   Section        0  ebtn.o(i.bit_array_or)
    bit_array_or                             0x08004d1b   Thumb Code    38  ebtn.o(i.bit_array_or)
    i.btn_task                               0x08004d40   Section        0  btn_app.o(i.btn_task)
    i.ebtn_combo_btn_add_btn_by_idx          0x08004d4e   Section        0  ebtn.o(i.ebtn_combo_btn_add_btn_by_idx)
    i.ebtn_get_btn_index_by_key_id           0x08004d70   Section        0  ebtn.o(i.ebtn_get_btn_index_by_key_id)
    i.ebtn_get_current_state                 0x08004db8   Section        0  ebtn.o(i.ebtn_get_current_state)
    ebtn_get_current_state                   0x08004db9   Thumb Code    82  ebtn.o(i.ebtn_get_current_state)
    i.ebtn_init                              0x08004e10   Section        0  ebtn.o(i.ebtn_init)
    i.ebtn_process                           0x08004e5c   Section        0  ebtn.o(i.ebtn_process)
    i.ebtn_process_btn                       0x08004e76   Section        0  ebtn.o(i.ebtn_process_btn)
    ebtn_process_btn                         0x08004e77   Thumb Code    56  ebtn.o(i.ebtn_process_btn)
    i.ebtn_process_btn_combo                 0x08004eae   Section        0  ebtn.o(i.ebtn_process_btn_combo)
    ebtn_process_btn_combo                   0x08004eaf   Thumb Code   116  ebtn.o(i.ebtn_process_btn_combo)
    i.ebtn_process_with_curr_state           0x08004f24   Section        0  ebtn.o(i.ebtn_process_with_curr_state)
    i.ebtn_set_config                        0x080050e4   Section        0  ebtn.o(i.ebtn_set_config)
    i.ebtn_timer_sub                         0x080050f0   Section        0  ebtn.o(i.ebtn_timer_sub)
    ebtn_timer_sub                           0x080050f1   Thumb Code     6  ebtn.o(i.ebtn_timer_sub)
    i.key_read                               0x080050f8   Section        0  key_app.o(i.key_read)
    i.key_task                               0x0800511c   Section        0  key_app.o(i.key_task)
    i.led_disp                               0x08005168   Section        0  led_app.o(i.led_disp)
    i.led_task                               0x080051e0   Section        0  led_app.o(i.led_task)
    i.main                                   0x080051f0   Section        0  main.o(i.main)
    i.my_printf                              0x08005258   Section        0  usart_app.o(i.my_printf)
    i.oled_task                              0x08005294   Section        0  oled_app.o(i.oled_task)
    i.prv_btn_event                          0x080052f8   Section        0  btn_app.o(i.prv_btn_event)
    i.prv_btn_get_state                      0x08005378   Section        0  btn_app.o(i.prv_btn_get_state)
    i.prv_process_btn                        0x080053b4   Section        0  ebtn.o(i.prv_process_btn)
    prv_process_btn                          0x080053b5   Thumb Code   482  ebtn.o(i.prv_process_btn)
    i.rt_ringbuffer_data_len                 0x0800559c   Section        0  ringbuffer.o(i.rt_ringbuffer_data_len)
    i.rt_ringbuffer_get                      0x080055d8   Section        0  ringbuffer.o(i.rt_ringbuffer_get)
    i.rt_ringbuffer_init                     0x080056c4   Section        0  ringbuffer.o(i.rt_ringbuffer_init)
    i.rt_ringbuffer_put                      0x08005758   Section        0  ringbuffer.o(i.rt_ringbuffer_put)
    i.rt_ringbuffer_status                   0x08005848   Section        0  ringbuffer.o(i.rt_ringbuffer_status)
    rt_ringbuffer_status                     0x08005849   Thumb Code    42  ringbuffer.o(i.rt_ringbuffer_status)
    i.scheduler_init                         0x08005874   Section        0  scheduler.o(i.scheduler_init)
    i.scheduler_run                          0x08005880   Section        0  scheduler.o(i.scheduler_run)
    i.u8g2_ClearBuffer                       0x080058d8   Section        0  u8g2_buffer.o(i.u8g2_ClearBuffer)
    i.u8g2_DrawCircle                        0x080058f2   Section        0  u8g2_circle.o(i.u8g2_DrawCircle)
    i.u8g2_DrawGlyph                         0x08005938   Section        0  u8g2_font.o(i.u8g2_DrawGlyph)
    i.u8g2_DrawHVLine                        0x0800599a   Section        0  u8g2_hvline.o(i.u8g2_DrawHVLine)
    i.u8g2_DrawPixel                         0x08005a76   Section        0  u8g2_hvline.o(i.u8g2_DrawPixel)
    i.u8g2_DrawStr                           0x08005abc   Section        0  u8g2_font.o(i.u8g2_DrawStr)
    i.u8g2_IsIntersection                    0x08005ae0   Section        0  u8g2_intersection.o(i.u8g2_IsIntersection)
    i.u8g2_SendBuffer                        0x08005b1a   Section        0  u8g2_buffer.o(i.u8g2_SendBuffer)
    i.u8g2_SetDrawColor                      0x08005b2c   Section        0  u8g2_hvline.o(i.u8g2_SetDrawColor)
    i.u8g2_SetFontPosBaseline                0x08005b3c   Section        0  u8g2_font.o(i.u8g2_SetFontPosBaseline)
    i.u8g2_SetMaxClipWindow                  0x08005b48   Section        0  u8g2_setup.o(i.u8g2_SetMaxClipWindow)
    i.u8g2_SetupBuffer                       0x08005b68   Section        0  u8g2_setup.o(i.u8g2_SetupBuffer)
    i.u8g2_Setup_ssd1315_i2c_128x64_noname_f 0x08005bc4   Section        0  u8g2_d_setup.o(i.u8g2_Setup_ssd1315_i2c_128x64_noname_f)
    i.u8g2_add_vector_x                      0x08005c08   Section        0  u8g2_font.o(i.u8g2_add_vector_x)
    i.u8g2_add_vector_y                      0x08005c32   Section        0  u8g2_font.o(i.u8g2_add_vector_y)
    i.u8g2_apply_clip_window                 0x08005c5c   Section        0  u8g2_setup.o(i.u8g2_apply_clip_window)
    u8g2_apply_clip_window                   0x08005c5d   Thumb Code   122  u8g2_setup.o(i.u8g2_apply_clip_window)
    i.u8g2_clip_intersection2                0x08005cd6   Section        0  u8g2_hvline.o(i.u8g2_clip_intersection2)
    u8g2_clip_intersection2                  0x08005cd7   Thumb Code    76  u8g2_hvline.o(i.u8g2_clip_intersection2)
    i.u8g2_draw_circle                       0x08005d22   Section        0  u8g2_circle.o(i.u8g2_draw_circle)
    u8g2_draw_circle                         0x08005d23   Thumb Code   128  u8g2_circle.o(i.u8g2_draw_circle)
    i.u8g2_draw_circle_section               0x08005da2   Section        0  u8g2_circle.o(i.u8g2_draw_circle_section)
    u8g2_draw_circle_section                 0x08005da3   Thumb Code   156  u8g2_circle.o(i.u8g2_draw_circle_section)
    i.u8g2_draw_hv_line_2dir                 0x08005e3e   Section        0  u8g2_hvline.o(i.u8g2_draw_hv_line_2dir)
    i.u8g2_draw_l90_mirrorr_r0               0x08005e6a   Section        0  u8g2_setup.o(i.u8g2_draw_l90_mirrorr_r0)
    i.u8g2_draw_l90_r0                       0x08005ea6   Section        0  u8g2_setup.o(i.u8g2_draw_l90_r0)
    i.u8g2_draw_l90_r1                       0x08005eca   Section        0  u8g2_setup.o(i.u8g2_draw_l90_r1)
    i.u8g2_draw_l90_r2                       0x08005f0e   Section        0  u8g2_setup.o(i.u8g2_draw_l90_r2)
    i.u8g2_draw_l90_r3                       0x08005f64   Section        0  u8g2_setup.o(i.u8g2_draw_l90_r3)
    i.u8g2_draw_mirror_vertical_r0           0x08005fb4   Section        0  u8g2_setup.o(i.u8g2_draw_mirror_vertical_r0)
    i.u8g2_draw_string                       0x08005ff0   Section        0  u8g2_font.o(i.u8g2_draw_string)
    u8g2_draw_string                         0x08005ff1   Thumb Code   144  u8g2_font.o(i.u8g2_draw_string)
    i.u8g2_font_calc_vref_font               0x08006080   Section        0  u8g2_font.o(i.u8g2_font_calc_vref_font)
    i.u8g2_font_decode_get_signed_bits       0x08006086   Section        0  u8g2_font.o(i.u8g2_font_decode_get_signed_bits)
    i.u8g2_font_decode_get_unsigned_bits     0x080060aa   Section        0  u8g2_font.o(i.u8g2_font_decode_get_unsigned_bits)
    i.u8g2_font_decode_glyph                 0x080060ec   Section        0  u8g2_font.o(i.u8g2_font_decode_glyph)
    i.u8g2_font_decode_len                   0x0800626e   Section        0  u8g2_font.o(i.u8g2_font_decode_len)
    i.u8g2_font_draw_glyph                   0x0800631e   Section        0  u8g2_font.o(i.u8g2_font_draw_glyph)
    u8g2_font_draw_glyph                     0x0800631f   Thumb Code    58  u8g2_font.o(i.u8g2_font_draw_glyph)
    i.u8g2_font_get_glyph_data               0x08006358   Section        0  u8g2_font.o(i.u8g2_font_get_glyph_data)
    i.u8g2_font_get_word                     0x080063ea   Section        0  u8g2_font.o(i.u8g2_font_get_word)
    u8g2_font_get_word                       0x080063eb   Thumb Code    24  u8g2_font.o(i.u8g2_font_get_word)
    i.u8g2_font_setup_decode                 0x08006402   Section        0  u8g2_font.o(i.u8g2_font_setup_decode)
    u8g2_font_setup_decode                   0x08006403   Thumb Code    64  u8g2_font.o(i.u8g2_font_setup_decode)
    i.u8g2_gpio_and_delay_stm32              0x08006442   Section        0  oled_app.o(i.u8g2_gpio_and_delay_stm32)
    i.u8g2_is_intersection_decision_tree     0x080064e2   Section        0  u8g2_intersection.o(i.u8g2_is_intersection_decision_tree)
    i.u8g2_ll_hvline_vertical_top_lsb        0x08006512   Section        0  u8g2_ll_hvline.o(i.u8g2_ll_hvline_vertical_top_lsb)
    i.u8g2_m_16_8_f                          0x08006600   Section        0  u8g2_d_memory.o(i.u8g2_m_16_8_f)
    i.u8g2_send_buffer                       0x08006610   Section        0  u8g2_buffer.o(i.u8g2_send_buffer)
    u8g2_send_buffer                         0x08006611   Thumb Code    54  u8g2_buffer.o(i.u8g2_send_buffer)
    i.u8g2_send_tile_row                     0x08006646   Section        0  u8g2_buffer.o(i.u8g2_send_tile_row)
    u8g2_send_tile_row                       0x08006647   Thumb Code    54  u8g2_buffer.o(i.u8g2_send_tile_row)
    i.u8g2_update_dimension_common           0x0800667c   Section        0  u8g2_setup.o(i.u8g2_update_dimension_common)
    u8g2_update_dimension_common             0x0800667d   Thumb Code    96  u8g2_setup.o(i.u8g2_update_dimension_common)
    i.u8g2_update_dimension_r0               0x080066dc   Section        0  u8g2_setup.o(i.u8g2_update_dimension_r0)
    i.u8g2_update_dimension_r1               0x080066e8   Section        0  u8g2_setup.o(i.u8g2_update_dimension_r1)
    i.u8g2_update_dimension_r2               0x08006704   Section        0  u8g2_setup.o(i.u8g2_update_dimension_r2)
    i.u8g2_update_dimension_r3               0x08006710   Section        0  u8g2_setup.o(i.u8g2_update_dimension_r3)
    i.u8g2_update_page_win_r0                0x0800672c   Section        0  u8g2_setup.o(i.u8g2_update_page_win_r0)
    i.u8g2_update_page_win_r1                0x0800674c   Section        0  u8g2_setup.o(i.u8g2_update_page_win_r1)
    i.u8g2_update_page_win_r2                0x0800676c   Section        0  u8g2_setup.o(i.u8g2_update_page_win_r2)
    i.u8g2_update_page_win_r3                0x080067a8   Section        0  u8g2_setup.o(i.u8g2_update_page_win_r3)
    i.u8x8_DrawTile                          0x080067e4   Section        0  u8x8_display.o(i.u8x8_DrawTile)
    i.u8x8_InitDisplay                       0x08006816   Section        0  u8x8_display.o(i.u8x8_InitDisplay)
    i.u8x8_RefreshDisplay                    0x08006828   Section        0  u8x8_display.o(i.u8x8_RefreshDisplay)
    i.u8x8_SetPowerSave                      0x0800683a   Section        0  u8x8_display.o(i.u8x8_SetPowerSave)
    i.u8x8_Setup                             0x0800684e   Section        0  u8x8_setup.o(i.u8x8_Setup)
    i.u8x8_SetupDefaults                     0x08006878   Section        0  u8x8_setup.o(i.u8x8_SetupDefaults)
    i.u8x8_SetupMemory                       0x080068a4   Section        0  u8x8_display.o(i.u8x8_SetupMemory)
    i.u8x8_ascii_next                        0x080068b6   Section        0  u8x8_8x8.o(i.u8x8_ascii_next)
    i.u8x8_byte_EndTransfer                  0x080068c8   Section        0  u8x8_byte.o(i.u8x8_byte_EndTransfer)
    i.u8x8_byte_SendByte                     0x080068da   Section        0  u8x8_byte.o(i.u8x8_byte_SendByte)
    i.u8x8_byte_SendBytes                    0x080068ea   Section        0  u8x8_byte.o(i.u8x8_byte_SendBytes)
    i.u8x8_byte_StartTransfer                0x08006904   Section        0  u8x8_byte.o(i.u8x8_byte_StartTransfer)
    i.u8x8_byte_hw_i2c                       0x08006918   Section        0  oled_app.o(i.u8x8_byte_hw_i2c)
    i.u8x8_cad_EndTransfer                   0x080069a0   Section        0  u8x8_cad.o(i.u8x8_cad_EndTransfer)
    i.u8x8_cad_SendArg                       0x080069b2   Section        0  u8x8_cad.o(i.u8x8_cad_SendArg)
    i.u8x8_cad_SendCmd                       0x080069c6   Section        0  u8x8_cad.o(i.u8x8_cad_SendCmd)
    i.u8x8_cad_SendData                      0x080069da   Section        0  u8x8_cad.o(i.u8x8_cad_SendData)
    i.u8x8_cad_SendSequence                  0x080069f4   Section        0  u8x8_cad.o(i.u8x8_cad_SendSequence)
    i.u8x8_cad_StartTransfer                 0x08006a70   Section        0  u8x8_cad.o(i.u8x8_cad_StartTransfer)
    i.u8x8_cad_ssd13xx_fast_i2c              0x08006a84   Section        0  u8x8_cad.o(i.u8x8_cad_ssd13xx_fast_i2c)
    i.u8x8_d_helper_display_init             0x08006b58   Section        0  u8x8_display.o(i.u8x8_d_helper_display_init)
    i.u8x8_d_helper_display_setup_memory     0x08006bb8   Section        0  u8x8_display.o(i.u8x8_d_helper_display_setup_memory)
    i.u8x8_d_ssd1315_128x64_noname           0x08006bc4   Section        0  u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_128x64_noname)
    i.u8x8_d_ssd1315_generic                 0x08006c18   Section        0  u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_generic)
    u8x8_d_ssd1315_generic                   0x08006c19   Thumb Code   242  u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_generic)
    i.u8x8_dummy_cb                          0x08006d1c   Section        0  u8x8_setup.o(i.u8x8_dummy_cb)
    i.u8x8_gpio_call                         0x08006d24   Section        0  u8x8_gpio.o(i.u8x8_gpio_call)
    i.u8x8_i2c_data_transfer                 0x08006d3e   Section        0  u8x8_cad.o(i.u8x8_i2c_data_transfer)
    u8x8_i2c_data_transfer                   0x08006d3f   Thumb Code    46  u8x8_cad.o(i.u8x8_i2c_data_transfer)
    i.u8x8_utf8_init                         0x08006d6c   Section        0  u8x8_8x8.o(i.u8x8_utf8_init)
    i.uart_task                              0x08006d74   Section        0  usart_app.o(i.uart_task)
    .constdata                               0x08006dc0   Section       18  stm32f1xx_hal_rcc.o(.constdata)
    aPLLMULFactorTable                       0x08006dc0   Data          16  stm32f1xx_hal_rcc.o(.constdata)
    aPredivFactorTable                       0x08006dd0   Data           2  stm32f1xx_hal_rcc.o(.constdata)
    .constdata                               0x08006dd2   Section       24  system_stm32f1xx.o(.constdata)
    .constdata                               0x08006dec   Section       17  led_app.o(.constdata)
    led_pin_configs                          0x08006dec   Data          16  led_app.o(.constdata)
    .constdata                               0x08006dfe   Section       28  btn_app.o(.constdata)
    combo_ebtn_param                         0x08006e0c   Data          14  btn_app.o(.constdata)
    .constdata                               0x08006e1c   Section       72  u8g2_setup.o(.constdata)
    .constdata                               0x08006e64   Section      100  u8x8_d_ssd1315_128x64_noname.o(.constdata)
    u8x8_d_ssd1315_128x64_noname_init_seq    0x08006e64   Data          51  u8x8_d_ssd1315_128x64_noname.o(.constdata)
    u8x8_d_ssd1315_128x64_noname_powersave0_seq 0x08006e97   Data           5  u8x8_d_ssd1315_128x64_noname.o(.constdata)
    u8x8_d_ssd1315_128x64_noname_powersave1_seq 0x08006e9c   Data           5  u8x8_d_ssd1315_128x64_noname.o(.constdata)
    u8x8_d_ssd1315_128x64_noname_flip0_seq   0x08006ea1   Data           7  u8x8_d_ssd1315_128x64_noname.o(.constdata)
    u8x8_d_ssd1315_128x64_noname_flip1_seq   0x08006ea8   Data           7  u8x8_d_ssd1315_128x64_noname.o(.constdata)
    u8x8_ssd1315_128x64_noname_display_info  0x08006eb0   Data          24  u8x8_d_ssd1315_128x64_noname.o(.constdata)
    .data                                    0x20000000   Section        9  stm32f1xx_hal.o(.data)
    .data                                    0x2000000c   Section        4  system_stm32f1xx.o(.data)
    .data                                    0x20000010   Section        4  key_app.o(.data)
    .data                                    0x20000014   Section        4  led_app.o(.data)
    temp_old                                 0x20000016   Data           1  led_app.o(.data)
    first_run                                0x20000017   Data           1  led_app.o(.data)
    .data                                    0x20000018   Section       64  scheduler.o(.data)
    scheduler_task                           0x2000001c   Data          60  scheduler.o(.data)
    .data                                    0x20000058   Section      156  btn_app.o(.data)
    btns                                     0x20000058   Data          84  btn_app.o(.data)
    btns_combo                               0x200000ac   Data          72  btn_app.o(.data)
    .data                                    0x200000f4   Section        1  oled_app.o(.data)
    buf_idx                                  0x200000f4   Data           1  oled_app.o(.data)
    .data                                    0x200000f5   Section       22  oled.o(.data)
    .data                                    0x2000010b   Section        6  u8x8_cad.o(.data)
    in_transfer                              0x2000010b   Data           1  u8x8_cad.o(.data)
    in_transfer                              0x2000010c   Data           1  u8x8_cad.o(.data)
    in_transfer                              0x2000010d   Data           1  u8x8_cad.o(.data)
    is_data                                  0x2000010e   Data           1  u8x8_cad.o(.data)
    in_transfer                              0x2000010f   Data           1  u8x8_cad.o(.data)
    is_data                                  0x20000110   Data           1  u8x8_cad.o(.data)
    .bss                                     0x20000114   Section       84  i2c.o(.bss)
    .bss                                     0x20000168   Section      140  usart.o(.bss)
    .bss                                     0x200001f4   Section      396  usart_app.o(.bss)
    .bss                                     0x20000380   Section      180  oled_app.o(.bss)
    buffer                                   0x20000414   Data          32  oled_app.o(.bss)
    .bss                                     0x20000434   Section       52  ebtn.o(.bss)
    ebtn_default                             0x20000434   Data          52  ebtn.o(.bss)
    .bss                                     0x20000468   Section     1024  u8g2_d_memory.o(.bss)
    buf                                      0x20000468   Data        1024  u8g2_d_memory.o(.bss)
    STACK                                    0x20000868   Section     1024  startup_stm32f103xe.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f103xe.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f103xe.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f103xe.o(RESET)
    __main                                   0x08000131   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000131   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000135   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000139   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000139   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000139   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000139   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x08000141   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x08000145   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x08000145   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x08000149   Thumb Code     8  startup_stm32f103xe.o(.text)
    ADC1_2_IRQHandler                        0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    ADC3_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    CAN1_RX1_IRQHandler                      0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    CAN1_SCE_IRQHandler                      0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel2_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel3_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel4_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel7_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel1_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel2_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel3_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel4_5_IRQHandler               0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI0_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI15_10_IRQHandler                     0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI1_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI2_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI3_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI4_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI9_5_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    FLASH_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    FSMC_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C1_ER_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C2_ER_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C2_EV_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    PVD_IRQHandler                           0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    RCC_IRQHandler                           0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    RTC_Alarm_IRQHandler                     0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    RTC_IRQHandler                           0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    SDIO_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    SPI1_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    SPI2_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    SPI3_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    TAMPER_IRQHandler                        0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_BRK_IRQHandler                      0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_CC_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_UP_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM2_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM3_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM4_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM5_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM6_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM7_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_BRK_IRQHandler                      0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_CC_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_UP_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    UART4_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    UART5_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    USART2_IRQHandler                        0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    USART3_IRQHandler                        0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    USBWakeUp_IRQHandler                     0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    WWDG_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f103xe.o(.text)
    __aeabi_llsr                             0x0800016d   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x0800016d   Thumb Code     0  llushr.o(.text)
    __aeabi_memcpy                           0x0800018d   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x0800018d   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x0800018d   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x080001b1   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x080001b1   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x080001b1   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x080001bf   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x080001bf   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x080001bf   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x080001c3   Thumb Code    18  memseta.o(.text)
    memcmp                                   0x080001d5   Thumb Code    26  memcmp.o(.text)
    __aeabi_uidiv                            0x080001ef   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x080001ef   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x0800021b   Thumb Code    98  uldiv.o(.text)
    __I$use$fp                               0x0800027d   Thumb Code     0  iusefp.o(.text)
    __aeabi_dadd                             0x0800027d   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080003bf   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080003c5   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x080003cb   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x080004af   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x0800058d   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x080005bd   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x080005ed   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080005ed   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x08000611   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000611   Thumb Code     0  llshl.o(.text)
    __aeabi_lasr                             0x0800062f   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x0800062f   Thumb Code     0  llsshr.o(.text)
    _double_round                            0x08000653   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08000671   Thumb Code   156  depilogue.o(.text)
    BusFault_Handler                         0x0800070d   Thumb Code     4  stm32f1xx_it.o(i.BusFault_Handler)
    DMA1_Channel5_IRQHandler                 0x08000711   Thumb Code    10  stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler)
    DebugMon_Handler                         0x0800074d   Thumb Code     2  stm32f1xx_it.o(i.DebugMon_Handler)
    Error_Handler                            0x0800074f   Thumb Code     6  main.o(i.Error_Handler)
    HAL_DMA_Abort                            0x08000755   Thumb Code    86  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x080007ad   Thumb Code   414  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_GetState                         0x08000955   Thumb Code     8  stm32f1xx_hal_dma.o(i.HAL_DMA_GetState)
    HAL_DMA_IRQHandler                       0x0800095d   Thumb Code   910  stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08000cf5   Thumb Code   132  stm32f1xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08000d81   Thumb Code   156  stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x08000e1d   Thumb Code    36  stm32f1xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x08000e45   Thumb Code   792  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08001185   Thumb Code    16  stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x08001195   Thumb Code    12  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x080011a1   Thumb Code     6  stm32f1xx_hal.o(i.HAL_GetTick)
    HAL_I2C_AbortCpltCallback                0x080011ad   Thumb Code     2  stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback)
    HAL_I2C_AddrCallback                     0x080011af   Thumb Code     2  stm32f1xx_hal_i2c.o(i.HAL_I2C_AddrCallback)
    HAL_I2C_EV_IRQHandler                    0x080011b1   Thumb Code   474  stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler)
    HAL_I2C_ErrorCallback                    0x0800138b   Thumb Code     2  stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback)
    HAL_I2C_Init                             0x0800138d   Thumb Code   446  stm32f1xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_ListenCpltCallback               0x0800155d   Thumb Code     2  stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback)
    HAL_I2C_MasterRxCpltCallback             0x0800155f   Thumb Code     2  stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback)
    HAL_I2C_MasterTxCpltCallback             0x08001561   Thumb Code     2  stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback)
    HAL_I2C_Master_Transmit                  0x08001565   Thumb Code   360  stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    HAL_I2C_MemRxCpltCallback                0x080016d5   Thumb Code     2  stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback)
    HAL_I2C_MemTxCpltCallback                0x080016d7   Thumb Code     2  stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback)
    HAL_I2C_Mem_Write                        0x080016d9   Thumb Code   348  stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    HAL_I2C_MspInit                          0x0800183d   Thumb Code   120  i2c.o(i.HAL_I2C_MspInit)
    HAL_I2C_SlaveRxCpltCallback              0x080018c1   Thumb Code     2  stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback)
    HAL_I2C_SlaveTxCpltCallback              0x080018c3   Thumb Code     2  stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback)
    HAL_IncTick                              0x080018c5   Thumb Code    16  stm32f1xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x080018dd   Thumb Code    34  stm32f1xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08001905   Thumb Code    64  stm32f1xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08001951   Thumb Code    76  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x080019a5   Thumb Code    32  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x080019c5   Thumb Code   124  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08001a41   Thumb Code    32  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08001a69   Thumb Code   376  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x08001bf5   Thumb Code     6  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x08001c01   Thumb Code    22  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08001c21   Thumb Code    22  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08001c41   Thumb Code    92  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08001cb1   Thumb Code  1114  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08002115   Thumb Code    52  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08002149   Thumb Code   112  stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x080021b9   Thumb Code    66  usart_app.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_DMAStop                         0x08002211   Thumb Code   138  stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x0800229b   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x0800229d   Thumb Code   736  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08002581   Thumb Code   118  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x080025f9   Thumb Code   188  usart.o(i.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x080026c9   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x080026cb   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x080026cd   Thumb Code   190  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x0800278b   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x0800278d   Thumb Code     4  stm32f1xx_it.o(i.HardFault_Handler)
    I2C1_EV_IRQHandler                       0x08002791   Thumb Code    10  stm32f1xx_it.o(i.I2C1_EV_IRQHandler)
    MX_DMA_Init                              0x080039a5   Thumb Code    48  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x080039d9   Thumb Code   186  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x08003aa1   Thumb Code    48  i2c.o(i.MX_I2C1_Init)
    MX_USART1_UART_Init                      0x08003add   Thumb Code    72  usart.o(i.MX_USART1_UART_Init)
    MemManage_Handler                        0x08003b35   Thumb Code     4  stm32f1xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08003b39   Thumb Code     4  stm32f1xx_it.o(i.NMI_Handler)
    OLED_Clear                               0x08003b3d   Thumb Code    56  oled.o(i.OLED_Clear)
    OLED_Init                                0x08003b75   Thumb Code    42  oled.o(i.OLED_Init)
    OLED_Set_Position                        0x08003ba5   Thumb Code    36  oled.o(i.OLED_Set_Position)
    OLED_Write_cmd                           0x08003bc9   Thumb Code    34  oled.o(i.OLED_Write_cmd)
    OLED_Write_data                          0x08003bf1   Thumb Code    34  oled.o(i.OLED_Write_data)
    PendSV_Handler                           0x08003c19   Thumb Code     2  stm32f1xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08003c45   Thumb Code     2  stm32f1xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08003c47   Thumb Code     8  stm32f1xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08003c4f   Thumb Code   102  main.o(i.SystemClock_Config)
    SystemInit                               0x08003cb5   Thumb Code     2  system_stm32f1xx.o(i.SystemInit)
    UART_Start_Receive_DMA                   0x0800409d   Thumb Code   186  stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA)
    USART1_IRQHandler                        0x08004251   Thumb Code    10  stm32f1xx_it.o(i.USART1_IRQHandler)
    UsageFault_Handler                       0x08004261   Thumb Code     4  stm32f1xx_it.o(i.UsageFault_Handler)
    __0vsnprintf                             0x08004265   Thumb Code    46  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x08004265   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x08004265   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x08004265   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x08004265   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __aeabi_assert                           0x080042d1   Thumb Code    26  usart_app.o(i.__aeabi_assert)
    __scatterload_copy                       0x08004319   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08004327   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08004329   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    app_ebtn_init                            0x08004bd9   Thumb Code    78  btn_app.o(i.app_ebtn_init)
    btn_task                                 0x08004d41   Thumb Code    14  btn_app.o(i.btn_task)
    ebtn_combo_btn_add_btn_by_idx            0x08004d4f   Thumb Code    32  ebtn.o(i.ebtn_combo_btn_add_btn_by_idx)
    ebtn_get_btn_index_by_key_id             0x08004d71   Thumb Code    66  ebtn.o(i.ebtn_get_btn_index_by_key_id)
    ebtn_init                                0x08004e11   Thumb Code    72  ebtn.o(i.ebtn_init)
    ebtn_process                             0x08004e5d   Thumb Code    26  ebtn.o(i.ebtn_process)
    ebtn_process_with_curr_state             0x08004f25   Thumb Code   444  ebtn.o(i.ebtn_process_with_curr_state)
    ebtn_set_config                          0x080050e5   Thumb Code     8  ebtn.o(i.ebtn_set_config)
    key_read                                 0x080050f9   Thumb Code    32  key_app.o(i.key_read)
    key_task                                 0x0800511d   Thumb Code    58  key_app.o(i.key_task)
    led_disp                                 0x08005169   Thumb Code   108  led_app.o(i.led_disp)
    led_task                                 0x080051e1   Thumb Code    10  led_app.o(i.led_task)
    main                                     0x080051f1   Thumb Code    80  main.o(i.main)
    my_printf                                0x08005259   Thumb Code    58  usart_app.o(i.my_printf)
    oled_task                                0x08005295   Thumb Code    64  oled_app.o(i.oled_task)
    prv_btn_event                            0x080052f9   Thumb Code    96  btn_app.o(i.prv_btn_event)
    prv_btn_get_state                        0x08005379   Thumb Code    54  btn_app.o(i.prv_btn_get_state)
    rt_ringbuffer_data_len                   0x0800559d   Thumb Code    60  ringbuffer.o(i.rt_ringbuffer_data_len)
    rt_ringbuffer_get                        0x080055d9   Thumb Code   180  ringbuffer.o(i.rt_ringbuffer_get)
    rt_ringbuffer_init                       0x080056c5   Thumb Code    84  ringbuffer.o(i.rt_ringbuffer_init)
    rt_ringbuffer_put                        0x08005759   Thumb Code   182  ringbuffer.o(i.rt_ringbuffer_put)
    scheduler_init                           0x08005875   Thumb Code     8  scheduler.o(i.scheduler_init)
    scheduler_run                            0x08005881   Thumb Code    78  scheduler.o(i.scheduler_run)
    u8g2_ClearBuffer                         0x080058d9   Thumb Code    26  u8g2_buffer.o(i.u8g2_ClearBuffer)
    u8g2_DrawCircle                          0x080058f3   Thumb Code    70  u8g2_circle.o(i.u8g2_DrawCircle)
    u8g2_DrawGlyph                           0x08005939   Thumb Code    98  u8g2_font.o(i.u8g2_DrawGlyph)
    u8g2_DrawHVLine                          0x0800599b   Thumb Code   220  u8g2_hvline.o(i.u8g2_DrawHVLine)
    u8g2_DrawPixel                           0x08005a77   Thumb Code    68  u8g2_hvline.o(i.u8g2_DrawPixel)
    u8g2_DrawStr                             0x08005abd   Thumb Code    32  u8g2_font.o(i.u8g2_DrawStr)
    u8g2_IsIntersection                      0x08005ae1   Thumb Code    58  u8g2_intersection.o(i.u8g2_IsIntersection)
    u8g2_SendBuffer                          0x08005b1b   Thumb Code    18  u8g2_buffer.o(i.u8g2_SendBuffer)
    u8g2_SetDrawColor                        0x08005b2d   Thumb Code    16  u8g2_hvline.o(i.u8g2_SetDrawColor)
    u8g2_SetFontPosBaseline                  0x08005b3d   Thumb Code     6  u8g2_font.o(i.u8g2_SetFontPosBaseline)
    u8g2_SetMaxClipWindow                    0x08005b49   Thumb Code    32  u8g2_setup.o(i.u8g2_SetMaxClipWindow)
    u8g2_SetupBuffer                         0x08005b69   Thumb Code    90  u8g2_setup.o(i.u8g2_SetupBuffer)
    u8g2_Setup_ssd1315_i2c_128x64_noname_f   0x08005bc5   Thumb Code    54  u8g2_d_setup.o(i.u8g2_Setup_ssd1315_i2c_128x64_noname_f)
    u8g2_add_vector_x                        0x08005c09   Thumb Code    42  u8g2_font.o(i.u8g2_add_vector_x)
    u8g2_add_vector_y                        0x08005c33   Thumb Code    42  u8g2_font.o(i.u8g2_add_vector_y)
    u8g2_draw_hv_line_2dir                   0x08005e3f   Thumb Code    44  u8g2_hvline.o(i.u8g2_draw_hv_line_2dir)
    u8g2_draw_l90_mirrorr_r0                 0x08005e6b   Thumb Code    60  u8g2_setup.o(i.u8g2_draw_l90_mirrorr_r0)
    u8g2_draw_l90_r0                         0x08005ea7   Thumb Code    36  u8g2_setup.o(i.u8g2_draw_l90_r0)
    u8g2_draw_l90_r1                         0x08005ecb   Thumb Code    68  u8g2_setup.o(i.u8g2_draw_l90_r1)
    u8g2_draw_l90_r2                         0x08005f0f   Thumb Code    86  u8g2_setup.o(i.u8g2_draw_l90_r2)
    u8g2_draw_l90_r3                         0x08005f65   Thumb Code    80  u8g2_setup.o(i.u8g2_draw_l90_r3)
    u8g2_draw_mirror_vertical_r0             0x08005fb5   Thumb Code    60  u8g2_setup.o(i.u8g2_draw_mirror_vertical_r0)
    u8g2_font_calc_vref_font                 0x08006081   Thumb Code     6  u8g2_font.o(i.u8g2_font_calc_vref_font)
    u8g2_font_decode_get_signed_bits         0x08006087   Thumb Code    36  u8g2_font.o(i.u8g2_font_decode_get_signed_bits)
    u8g2_font_decode_get_unsigned_bits       0x080060ab   Thumb Code    66  u8g2_font.o(i.u8g2_font_decode_get_unsigned_bits)
    u8g2_font_decode_glyph                   0x080060ed   Thumb Code   386  u8g2_font.o(i.u8g2_font_decode_glyph)
    u8g2_font_decode_len                     0x0800626f   Thumb Code   176  u8g2_font.o(i.u8g2_font_decode_len)
    u8g2_font_get_glyph_data                 0x08006359   Thumb Code   146  u8g2_font.o(i.u8g2_font_get_glyph_data)
    u8g2_gpio_and_delay_stm32                0x08006443   Thumb Code   160  oled_app.o(i.u8g2_gpio_and_delay_stm32)
    u8g2_is_intersection_decision_tree       0x080064e3   Thumb Code    48  u8g2_intersection.o(i.u8g2_is_intersection_decision_tree)
    u8g2_ll_hvline_vertical_top_lsb          0x08006513   Thumb Code   238  u8g2_ll_hvline.o(i.u8g2_ll_hvline_vertical_top_lsb)
    u8g2_m_16_8_f                            0x08006601   Thumb Code    10  u8g2_d_memory.o(i.u8g2_m_16_8_f)
    u8g2_update_dimension_r0                 0x080066dd   Thumb Code    12  u8g2_setup.o(i.u8g2_update_dimension_r0)
    u8g2_update_dimension_r1                 0x080066e9   Thumb Code    28  u8g2_setup.o(i.u8g2_update_dimension_r1)
    u8g2_update_dimension_r2                 0x08006705   Thumb Code    12  u8g2_setup.o(i.u8g2_update_dimension_r2)
    u8g2_update_dimension_r3                 0x08006711   Thumb Code    28  u8g2_setup.o(i.u8g2_update_dimension_r3)
    u8g2_update_page_win_r0                  0x0800672d   Thumb Code    32  u8g2_setup.o(i.u8g2_update_page_win_r0)
    u8g2_update_page_win_r1                  0x0800674d   Thumb Code    32  u8g2_setup.o(i.u8g2_update_page_win_r1)
    u8g2_update_page_win_r2                  0x0800676d   Thumb Code    60  u8g2_setup.o(i.u8g2_update_page_win_r2)
    u8g2_update_page_win_r3                  0x080067a9   Thumb Code    60  u8g2_setup.o(i.u8g2_update_page_win_r3)
    u8x8_DrawTile                            0x080067e5   Thumb Code    50  u8x8_display.o(i.u8x8_DrawTile)
    u8x8_InitDisplay                         0x08006817   Thumb Code    18  u8x8_display.o(i.u8x8_InitDisplay)
    u8x8_RefreshDisplay                      0x08006829   Thumb Code    18  u8x8_display.o(i.u8x8_RefreshDisplay)
    u8x8_SetPowerSave                        0x0800683b   Thumb Code    20  u8x8_display.o(i.u8x8_SetPowerSave)
    u8x8_Setup                               0x0800684f   Thumb Code    42  u8x8_setup.o(i.u8x8_Setup)
    u8x8_SetupDefaults                       0x08006879   Thumb Code    38  u8x8_setup.o(i.u8x8_SetupDefaults)
    u8x8_SetupMemory                         0x080068a5   Thumb Code    18  u8x8_display.o(i.u8x8_SetupMemory)
    u8x8_ascii_next                          0x080068b7   Thumb Code    18  u8x8_8x8.o(i.u8x8_ascii_next)
    u8x8_byte_EndTransfer                    0x080068c9   Thumb Code    18  u8x8_byte.o(i.u8x8_byte_EndTransfer)
    u8x8_byte_SendByte                       0x080068db   Thumb Code    16  u8x8_byte.o(i.u8x8_byte_SendByte)
    u8x8_byte_SendBytes                      0x080068eb   Thumb Code    26  u8x8_byte.o(i.u8x8_byte_SendBytes)
    u8x8_byte_StartTransfer                  0x08006905   Thumb Code    18  u8x8_byte.o(i.u8x8_byte_StartTransfer)
    u8x8_byte_hw_i2c                         0x08006919   Thumb Code   122  oled_app.o(i.u8x8_byte_hw_i2c)
    u8x8_cad_EndTransfer                     0x080069a1   Thumb Code    18  u8x8_cad.o(i.u8x8_cad_EndTransfer)
    u8x8_cad_SendArg                         0x080069b3   Thumb Code    20  u8x8_cad.o(i.u8x8_cad_SendArg)
    u8x8_cad_SendCmd                         0x080069c7   Thumb Code    20  u8x8_cad.o(i.u8x8_cad_SendCmd)
    u8x8_cad_SendData                        0x080069db   Thumb Code    26  u8x8_cad.o(i.u8x8_cad_SendData)
    u8x8_cad_SendSequence                    0x080069f5   Thumb Code   124  u8x8_cad.o(i.u8x8_cad_SendSequence)
    u8x8_cad_StartTransfer                   0x08006a71   Thumb Code    18  u8x8_cad.o(i.u8x8_cad_StartTransfer)
    u8x8_cad_ssd13xx_fast_i2c                0x08006a85   Thumb Code   208  u8x8_cad.o(i.u8x8_cad_ssd13xx_fast_i2c)
    u8x8_d_helper_display_init               0x08006b59   Thumb Code    96  u8x8_display.o(i.u8x8_d_helper_display_init)
    u8x8_d_helper_display_setup_memory       0x08006bb9   Thumb Code    12  u8x8_display.o(i.u8x8_d_helper_display_setup_memory)
    u8x8_d_ssd1315_128x64_noname             0x08006bc5   Thumb Code    76  u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_128x64_noname)
    u8x8_dummy_cb                            0x08006d1d   Thumb Code     8  u8x8_setup.o(i.u8x8_dummy_cb)
    u8x8_gpio_call                           0x08006d25   Thumb Code    26  u8x8_gpio.o(i.u8x8_gpio_call)
    u8x8_utf8_init                           0x08006d6d   Thumb Code     8  u8x8_8x8.o(i.u8x8_utf8_init)
    uart_task                                0x08006d75   Thumb Code    46  usart_app.o(i.uart_task)
    AHBPrescTable                            0x08006dd2   Data          16  system_stm32f1xx.o(.constdata)
    APBPrescTable                            0x08006de2   Data           8  system_stm32f1xx.o(.constdata)
    LED_COUNT                                0x08006dfc   Data           1  led_app.o(.constdata)
    default_ebtn_param                       0x08006dfe   Data          14  btn_app.o(.constdata)
    u8g2_cb_r0                               0x08006e1c   Data          12  u8g2_setup.o(.constdata)
    u8g2_cb_r1                               0x08006e28   Data          12  u8g2_setup.o(.constdata)
    u8g2_cb_r2                               0x08006e34   Data          12  u8g2_setup.o(.constdata)
    u8g2_cb_r3                               0x08006e40   Data          12  u8g2_setup.o(.constdata)
    u8g2_cb_mirror                           0x08006e4c   Data          12  u8g2_setup.o(.constdata)
    u8g2_cb_mirror_vertical                  0x08006e58   Data          12  u8g2_setup.o(.constdata)
    Region$$Table$$Base                      0x08006ec8   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08006ee8   Number         0  anon$$obj.o(Region$$Table)
    uwTick                                   0x20000000   Data           4  stm32f1xx_hal.o(.data)
    uwTickPrio                               0x20000004   Data           4  stm32f1xx_hal.o(.data)
    uwTickFreq                               0x20000008   Data           1  stm32f1xx_hal.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f1xx.o(.data)
    key_val                                  0x20000010   Data           1  key_app.o(.data)
    key_old                                  0x20000011   Data           1  key_app.o(.data)
    key_down                                 0x20000012   Data           1  key_app.o(.data)
    key_up                                   0x20000013   Data           1  key_app.o(.data)
    ucLed                                    0x20000014   Data           2  led_app.o(.data)
    task_num                                 0x20000018   Data           1  scheduler.o(.data)
    initcmd1                                 0x200000f5   Data          22  oled.o(.data)
    hi2c1                                    0x20000114   Data          84  i2c.o(.bss)
    huart1                                   0x20000168   Data          72  usart.o(.bss)
    hdma_usart1_rx                           0x200001b0   Data          68  usart.o(.bss)
    uart_ringbuffer                          0x200001f4   Data          12  usart_app.o(.bss)
    ringbuffer_pool                          0x20000200   Data         128  usart_app.o(.bss)
    uart_rx_dma_buffer                       0x20000280   Data         128  usart_app.o(.bss)
    uart_dma_buffer                          0x20000300   Data         128  usart_app.o(.bss)
    u8g2                                     0x20000380   Data         148  oled_app.o(.bss)
    __initial_sp                             0x20000c68   Data           0  startup_stm32f103xe.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00006ffc, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00006ee8, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO            3    RESET               startup_stm32f103xe.o
    0x08000130   0x08000130   0x00000000   Code   RO         5872  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000130   0x08000130   0x00000004   Code   RO         5923    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000134   0x08000134   0x00000004   Code   RO         5926    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000138   0x08000138   0x00000000   Code   RO         5928    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000138   0x08000138   0x00000000   Code   RO         5930    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000138   0x08000138   0x00000008   Code   RO         5931    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000140   0x08000140   0x00000004   Code   RO         5938    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x08000144   0x08000144   0x00000000   Code   RO         5933    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x08000144   0x08000144   0x00000000   Code   RO         5935    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x08000144   0x08000144   0x00000004   Code   RO         5924    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000148   0x08000148   0x00000024   Code   RO            4    .text               startup_stm32f103xe.o
    0x0800016c   0x0800016c   0x00000020   Code   RO         5875    .text               mc_w.l(llushr.o)
    0x0800018c   0x0800018c   0x00000024   Code   RO         5877    .text               mc_w.l(memcpya.o)
    0x080001b0   0x080001b0   0x00000024   Code   RO         5879    .text               mc_w.l(memseta.o)
    0x080001d4   0x080001d4   0x0000001a   Code   RO         5881    .text               mc_w.l(memcmp.o)
    0x080001ee   0x080001ee   0x0000002c   Code   RO         5942    .text               mc_w.l(uidiv.o)
    0x0800021a   0x0800021a   0x00000062   Code   RO         5944    .text               mc_w.l(uldiv.o)
    0x0800027c   0x0800027c   0x00000000   Code   RO         5946    .text               mc_w.l(iusefp.o)
    0x0800027c   0x0800027c   0x0000014e   Code   RO         5949    .text               mf_w.l(dadd.o)
    0x080003ca   0x080003ca   0x000000e4   Code   RO         5951    .text               mf_w.l(dmul.o)
    0x080004ae   0x080004ae   0x000000de   Code   RO         5953    .text               mf_w.l(ddiv.o)
    0x0800058c   0x0800058c   0x00000030   Code   RO         5955    .text               mf_w.l(dfixul.o)
    0x080005bc   0x080005bc   0x00000030   Code   RO         5957    .text               mf_w.l(cdrcmple.o)
    0x080005ec   0x080005ec   0x00000024   Code   RO         5959    .text               mc_w.l(init.o)
    0x08000610   0x08000610   0x0000001e   Code   RO         5962    .text               mc_w.l(llshl.o)
    0x0800062e   0x0800062e   0x00000024   Code   RO         5964    .text               mc_w.l(llsshr.o)
    0x08000652   0x08000652   0x000000ba   Code   RO         5967    .text               mf_w.l(depilogue.o)
    0x0800070c   0x0800070c   0x00000004   Code   RO          341    i.BusFault_Handler  stm32f1xx_it.o
    0x08000710   0x08000710   0x00000010   Code   RO          342    i.DMA1_Channel5_IRQHandler  stm32f1xx_it.o
    0x08000720   0x08000720   0x0000002c   Code   RO         1357    i.DMA_SetConfig     stm32f1xx_hal_dma.o
    0x0800074c   0x0800074c   0x00000002   Code   RO          343    i.DebugMon_Handler  stm32f1xx_it.o
    0x0800074e   0x0800074e   0x00000006   Code   RO           13    i.Error_Handler     main.o
    0x08000754   0x08000754   0x00000056   Code   RO         1358    i.HAL_DMA_Abort     stm32f1xx_hal_dma.o
    0x080007aa   0x080007aa   0x00000002   PAD
    0x080007ac   0x080007ac   0x000001a8   Code   RO         1359    i.HAL_DMA_Abort_IT  stm32f1xx_hal_dma.o
    0x08000954   0x08000954   0x00000008   Code   RO         1362    i.HAL_DMA_GetState  stm32f1xx_hal_dma.o
    0x0800095c   0x0800095c   0x00000398   Code   RO         1363    i.HAL_DMA_IRQHandler  stm32f1xx_hal_dma.o
    0x08000cf4   0x08000cf4   0x0000008c   Code   RO         1364    i.HAL_DMA_Init      stm32f1xx_hal_dma.o
    0x08000d80   0x08000d80   0x0000009c   Code   RO         1368    i.HAL_DMA_Start_IT  stm32f1xx_hal_dma.o
    0x08000e1c   0x08000e1c   0x00000028   Code   RO          979    i.HAL_Delay         stm32f1xx_hal.o
    0x08000e44   0x08000e44   0x00000340   Code   RO         1294    i.HAL_GPIO_Init     stm32f1xx_hal_gpio.o
    0x08001184   0x08001184   0x00000010   Code   RO         1296    i.HAL_GPIO_ReadPin  stm32f1xx_hal_gpio.o
    0x08001194   0x08001194   0x0000000c   Code   RO         1298    i.HAL_GPIO_WritePin  stm32f1xx_hal_gpio.o
    0x080011a0   0x080011a0   0x0000000c   Code   RO          983    i.HAL_GetTick       stm32f1xx_hal.o
    0x080011ac   0x080011ac   0x00000002   Code   RO          495    i.HAL_I2C_AbortCpltCallback  stm32f1xx_hal_i2c.o
    0x080011ae   0x080011ae   0x00000002   Code   RO          496    i.HAL_I2C_AddrCallback  stm32f1xx_hal_i2c.o
    0x080011b0   0x080011b0   0x000001da   Code   RO          500    i.HAL_I2C_EV_IRQHandler  stm32f1xx_hal_i2c.o
    0x0800138a   0x0800138a   0x00000002   Code   RO          502    i.HAL_I2C_ErrorCallback  stm32f1xx_hal_i2c.o
    0x0800138c   0x0800138c   0x000001d0   Code   RO          506    i.HAL_I2C_Init      stm32f1xx_hal_i2c.o
    0x0800155c   0x0800155c   0x00000002   Code   RO          508    i.HAL_I2C_ListenCpltCallback  stm32f1xx_hal_i2c.o
    0x0800155e   0x0800155e   0x00000002   Code   RO          509    i.HAL_I2C_MasterRxCpltCallback  stm32f1xx_hal_i2c.o
    0x08001560   0x08001560   0x00000002   Code   RO          510    i.HAL_I2C_MasterTxCpltCallback  stm32f1xx_hal_i2c.o
    0x08001562   0x08001562   0x00000002   PAD
    0x08001564   0x08001564   0x00000170   Code   RO          519    i.HAL_I2C_Master_Transmit  stm32f1xx_hal_i2c.o
    0x080016d4   0x080016d4   0x00000002   Code   RO          522    i.HAL_I2C_MemRxCpltCallback  stm32f1xx_hal_i2c.o
    0x080016d6   0x080016d6   0x00000002   Code   RO          523    i.HAL_I2C_MemTxCpltCallback  stm32f1xx_hal_i2c.o
    0x080016d8   0x080016d8   0x00000164   Code   RO          527    i.HAL_I2C_Mem_Write  stm32f1xx_hal_i2c.o
    0x0800183c   0x0800183c   0x00000084   Code   RO          254    i.HAL_I2C_MspInit   i2c.o
    0x080018c0   0x080018c0   0x00000002   Code   RO          532    i.HAL_I2C_SlaveRxCpltCallback  stm32f1xx_hal_i2c.o
    0x080018c2   0x080018c2   0x00000002   Code   RO          533    i.HAL_I2C_SlaveTxCpltCallback  stm32f1xx_hal_i2c.o
    0x080018c4   0x080018c4   0x00000018   Code   RO          989    i.HAL_IncTick       stm32f1xx_hal.o
    0x080018dc   0x080018dc   0x00000028   Code   RO          990    i.HAL_Init          stm32f1xx_hal.o
    0x08001904   0x08001904   0x0000004c   Code   RO          991    i.HAL_InitTick      stm32f1xx_hal.o
    0x08001950   0x08001950   0x00000054   Code   RO          435    i.HAL_MspInit       stm32f1xx_hal_msp.o
    0x080019a4   0x080019a4   0x00000020   Code   RO         1454    i.HAL_NVIC_EnableIRQ  stm32f1xx_hal_cortex.o
    0x080019c4   0x080019c4   0x0000007c   Code   RO         1460    i.HAL_NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08001a40   0x08001a40   0x00000028   Code   RO         1461    i.HAL_NVIC_SetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x08001a68   0x08001a68   0x0000018c   Code   RO         1147    i.HAL_RCC_ClockConfig  stm32f1xx_hal_rcc.o
    0x08001bf4   0x08001bf4   0x0000000c   Code   RO         1152    i.HAL_RCC_GetHCLKFreq  stm32f1xx_hal_rcc.o
    0x08001c00   0x08001c00   0x00000020   Code   RO         1154    i.HAL_RCC_GetPCLK1Freq  stm32f1xx_hal_rcc.o
    0x08001c20   0x08001c20   0x00000020   Code   RO         1155    i.HAL_RCC_GetPCLK2Freq  stm32f1xx_hal_rcc.o
    0x08001c40   0x08001c40   0x00000070   Code   RO         1156    i.HAL_RCC_GetSysClockFreq  stm32f1xx_hal_rcc.o
    0x08001cb0   0x08001cb0   0x00000464   Code   RO         1159    i.HAL_RCC_OscConfig  stm32f1xx_hal_rcc.o
    0x08002114   0x08002114   0x00000034   Code   RO         1465    i.HAL_SYSTICK_Config  stm32f1xx_hal_cortex.o
    0x08002148   0x08002148   0x00000070   Code   RO         1990    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f1xx_hal_uart.o
    0x080021b8   0x080021b8   0x00000058   Code   RO         2582    i.HAL_UARTEx_RxEventCallback  usart_app.o
    0x08002210   0x08002210   0x0000008a   Code   RO         2004    i.HAL_UART_DMAStop  stm32f1xx_hal_uart.o
    0x0800229a   0x0800229a   0x00000002   Code   RO         2006    i.HAL_UART_ErrorCallback  stm32f1xx_hal_uart.o
    0x0800229c   0x0800229c   0x000002e4   Code   RO         2009    i.HAL_UART_IRQHandler  stm32f1xx_hal_uart.o
    0x08002580   0x08002580   0x00000076   Code   RO         2010    i.HAL_UART_Init     stm32f1xx_hal_uart.o
    0x080025f6   0x080025f6   0x00000002   PAD
    0x080025f8   0x080025f8   0x000000d0   Code   RO          296    i.HAL_UART_MspInit  usart.o
    0x080026c8   0x080026c8   0x00000002   Code   RO         2016    i.HAL_UART_RxCpltCallback  stm32f1xx_hal_uart.o
    0x080026ca   0x080026ca   0x00000002   Code   RO         2017    i.HAL_UART_RxHalfCpltCallback  stm32f1xx_hal_uart.o
    0x080026cc   0x080026cc   0x000000be   Code   RO         2018    i.HAL_UART_Transmit  stm32f1xx_hal_uart.o
    0x0800278a   0x0800278a   0x00000002   Code   RO         2021    i.HAL_UART_TxCpltCallback  stm32f1xx_hal_uart.o
    0x0800278c   0x0800278c   0x00000004   Code   RO          344    i.HardFault_Handler  stm32f1xx_it.o
    0x08002790   0x08002790   0x00000010   Code   RO          345    i.I2C1_EV_IRQHandler  stm32f1xx_it.o
    0x080027a0   0x080027a0   0x0000001c   Code   RO          544    i.I2C_ConvertOtherXferOptions  stm32f1xx_hal_i2c.o
    0x080027bc   0x080027bc   0x000000f8   Code   RO          545    i.I2C_DMAAbort      stm32f1xx_hal_i2c.o
    0x080028b4   0x080028b4   0x00000012   Code   RO          548    i.I2C_Flush_DR      stm32f1xx_hal_i2c.o
    0x080028c6   0x080028c6   0x00000002   PAD
    0x080028c8   0x080028c8   0x000001ac   Code   RO          549    i.I2C_ITError       stm32f1xx_hal_i2c.o
    0x08002a74   0x08002a74   0x0000003e   Code   RO          550    i.I2C_IsAcknowledgeFailed  stm32f1xx_hal_i2c.o
    0x08002ab2   0x08002ab2   0x00000130   Code   RO          551    i.I2C_MasterReceive_BTF  stm32f1xx_hal_i2c.o
    0x08002be2   0x08002be2   0x00000104   Code   RO          552    i.I2C_MasterReceive_RXNE  stm32f1xx_hal_i2c.o
    0x08002ce6   0x08002ce6   0x00000002   PAD
    0x08002ce8   0x08002ce8   0x000000c0   Code   RO          554    i.I2C_MasterRequestWrite  stm32f1xx_hal_i2c.o
    0x08002da8   0x08002da8   0x000000a0   Code   RO          555    i.I2C_MasterTransmit_BTF  stm32f1xx_hal_i2c.o
    0x08002e48   0x08002e48   0x000000d8   Code   RO          556    i.I2C_MasterTransmit_TXE  stm32f1xx_hal_i2c.o
    0x08002f20   0x08002f20   0x0000002a   Code   RO          557    i.I2C_Master_ADD10  stm32f1xx_hal_i2c.o
    0x08002f4a   0x08002f4a   0x00000280   Code   RO          558    i.I2C_Master_ADDR   stm32f1xx_hal_i2c.o
    0x080031ca   0x080031ca   0x0000009e   Code   RO          559    i.I2C_Master_SB     stm32f1xx_hal_i2c.o
    0x08003268   0x08003268   0x000000c6   Code   RO          560    i.I2C_MemoryTransmit_TXE_BTF  stm32f1xx_hal_i2c.o
    0x0800332e   0x0800332e   0x00000002   PAD
    0x08003330   0x08003330   0x000000e0   Code   RO          562    i.I2C_RequestMemoryWrite  stm32f1xx_hal_i2c.o
    0x08003410   0x08003410   0x0000001a   Code   RO          563    i.I2C_SlaveReceive_BTF  stm32f1xx_hal_i2c.o
    0x0800342a   0x0800342a   0x00000046   Code   RO          564    i.I2C_SlaveReceive_RXNE  stm32f1xx_hal_i2c.o
    0x08003470   0x08003470   0x0000001a   Code   RO          565    i.I2C_SlaveTransmit_BTF  stm32f1xx_hal_i2c.o
    0x0800348a   0x0800348a   0x00000046   Code   RO          566    i.I2C_SlaveTransmit_TXE  stm32f1xx_hal_i2c.o
    0x080034d0   0x080034d0   0x00000070   Code   RO          567    i.I2C_Slave_ADDR    stm32f1xx_hal_i2c.o
    0x08003540   0x08003540   0x0000018c   Code   RO          569    i.I2C_Slave_STOPF   stm32f1xx_hal_i2c.o
    0x080036cc   0x080036cc   0x00000066   Code   RO          570    i.I2C_WaitOnBTFFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x08003732   0x08003732   0x000000be   Code   RO          571    i.I2C_WaitOnFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x080037f0   0x080037f0   0x000000fa   Code   RO          572    i.I2C_WaitOnMasterAddressFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x080038ea   0x080038ea   0x00000002   PAD
    0x080038ec   0x080038ec   0x00000050   Code   RO          575    i.I2C_WaitOnSTOPRequestThroughIT  stm32f1xx_hal_i2c.o
    0x0800393c   0x0800393c   0x00000066   Code   RO          576    i.I2C_WaitOnTXEFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x080039a2   0x080039a2   0x00000002   PAD
    0x080039a4   0x080039a4   0x00000034   Code   RO          229    i.MX_DMA_Init       dma.o
    0x080039d8   0x080039d8   0x000000c8   Code   RO          205    i.MX_GPIO_Init      gpio.o
    0x08003aa0   0x08003aa0   0x0000003c   Code   RO          255    i.MX_I2C1_Init      i2c.o
    0x08003adc   0x08003adc   0x00000058   Code   RO          297    i.MX_USART1_UART_Init  usart.o
    0x08003b34   0x08003b34   0x00000004   Code   RO          346    i.MemManage_Handler  stm32f1xx_it.o
    0x08003b38   0x08003b38   0x00000004   Code   RO          347    i.NMI_Handler       stm32f1xx_it.o
    0x08003b3c   0x08003b3c   0x00000038   Code   RO         2950    i.OLED_Clear        oled.o
    0x08003b74   0x08003b74   0x00000030   Code   RO         2953    i.OLED_Init         oled.o
    0x08003ba4   0x08003ba4   0x00000024   Code   RO         2955    i.OLED_Set_Position  oled.o
    0x08003bc8   0x08003bc8   0x00000028   Code   RO         2963    i.OLED_Write_cmd    oled.o
    0x08003bf0   0x08003bf0   0x00000028   Code   RO         2964    i.OLED_Write_data   oled.o
    0x08003c18   0x08003c18   0x00000002   Code   RO          348    i.PendSV_Handler    stm32f1xx_it.o
    0x08003c1a   0x08003c1a   0x00000002   PAD
    0x08003c1c   0x08003c1c   0x00000028   Code   RO         1160    i.RCC_Delay         stm32f1xx_hal_rcc.o
    0x08003c44   0x08003c44   0x00000002   Code   RO          349    i.SVC_Handler       stm32f1xx_it.o
    0x08003c46   0x08003c46   0x00000008   Code   RO          350    i.SysTick_Handler   stm32f1xx_it.o
    0x08003c4e   0x08003c4e   0x00000066   Code   RO           14    i.SystemClock_Config  main.o
    0x08003cb4   0x08003cb4   0x00000002   Code   RO         2354    i.SystemInit        system_stm32f1xx.o
    0x08003cb6   0x08003cb6   0x00000014   Code   RO         2023    i.UART_DMAAbortOnError  stm32f1xx_hal_uart.o
    0x08003cca   0x08003cca   0x00000050   Code   RO         2024    i.UART_DMAError     stm32f1xx_hal_uart.o
    0x08003d1a   0x08003d1a   0x000000b4   Code   RO         2025    i.UART_DMAReceiveCplt  stm32f1xx_hal_uart.o
    0x08003dce   0x08003dce   0x00000024   Code   RO         2027    i.UART_DMARxHalfCplt  stm32f1xx_hal_uart.o
    0x08003df2   0x08003df2   0x0000006c   Code   RO         2033    i.UART_EndRxTransfer  stm32f1xx_hal_uart.o
    0x08003e5e   0x08003e5e   0x00000020   Code   RO         2034    i.UART_EndTransmit_IT  stm32f1xx_hal_uart.o
    0x08003e7e   0x08003e7e   0x00000026   Code   RO         2035    i.UART_EndTxTransfer  stm32f1xx_hal_uart.o
    0x08003ea4   0x08003ea4   0x000000fc   Code   RO         2036    i.UART_Receive_IT   stm32f1xx_hal_uart.o
    0x08003fa0   0x08003fa0   0x000000fc   Code   RO         2037    i.UART_SetConfig    stm32f1xx_hal_uart.o
    0x0800409c   0x0800409c   0x000000c8   Code   RO         2038    i.UART_Start_Receive_DMA  stm32f1xx_hal_uart.o
    0x08004164   0x08004164   0x00000060   Code   RO         2040    i.UART_Transmit_IT  stm32f1xx_hal_uart.o
    0x080041c4   0x080041c4   0x0000008c   Code   RO         2041    i.UART_WaitOnFlagUntilTimeout  stm32f1xx_hal_uart.o
    0x08004250   0x08004250   0x00000010   Code   RO          351    i.USART1_IRQHandler  stm32f1xx_it.o
    0x08004260   0x08004260   0x00000004   Code   RO          352    i.UsageFault_Handler  stm32f1xx_it.o
    0x08004264   0x08004264   0x00000034   Code   RO         5891    i.__0vsnprintf      mc_w.l(printfa.o)
    0x08004298   0x08004298   0x00000010   Code   RO         1467    i.__NVIC_GetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x080042a8   0x080042a8   0x00000028   Code   RO         1468    i.__NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x080042d0   0x080042d0   0x00000048   Code   RO         2583    i.__aeabi_assert    usart_app.o
    0x08004318   0x08004318   0x0000000e   Code   RO         5971    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08004326   0x08004326   0x00000002   Code   RO         5972    i.__scatterload_null  mc_w.l(handlers.o)
    0x08004328   0x08004328   0x0000000e   Code   RO         5973    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08004336   0x08004336   0x00000002   PAD
    0x08004338   0x08004338   0x00000184   Code   RO         5893    i._fp_digits        mc_w.l(printfa.o)
    0x080044bc   0x080044bc   0x000006b4   Code   RO         5894    i._printf_core      mc_w.l(printfa.o)
    0x08004b70   0x08004b70   0x00000024   Code   RO         5895    i._printf_post_padding  mc_w.l(printfa.o)
    0x08004b94   0x08004b94   0x0000002e   Code   RO         5896    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08004bc2   0x08004bc2   0x00000016   Code   RO         5897    i._snputc           mc_w.l(printfa.o)
    0x08004bd8   0x08004bd8   0x00000060   Code   RO         2530    i.app_ebtn_init     btn_app.o
    0x08004c38   0x08004c38   0x00000026   Code   RO         2678    i.bit_array_and     ebtn.o
    0x08004c5e   0x08004c5e   0x0000002e   Code   RO         2679    i.bit_array_assign  ebtn.o
    0x08004c8c   0x08004c8c   0x00000024   Code   RO         2680    i.bit_array_cmp     ebtn.o
    0x08004cb0   0x08004cb0   0x00000016   Code   RO         2681    i.bit_array_get     ebtn.o
    0x08004cc6   0x08004cc6   0x00000054   Code   RO         2682    i.bit_array_num_bits_set  ebtn.o
    0x08004d1a   0x08004d1a   0x00000026   Code   RO         2683    i.bit_array_or      ebtn.o
    0x08004d40   0x08004d40   0x0000000e   Code   RO         2531    i.btn_task          btn_app.o
    0x08004d4e   0x08004d4e   0x00000020   Code   RO         2685    i.ebtn_combo_btn_add_btn_by_idx  ebtn.o
    0x08004d6e   0x08004d6e   0x00000002   PAD
    0x08004d70   0x08004d70   0x00000048   Code   RO         2692    i.ebtn_get_btn_index_by_key_id  ebtn.o
    0x08004db8   0x08004db8   0x00000058   Code   RO         2694    i.ebtn_get_current_state  ebtn.o
    0x08004e10   0x08004e10   0x0000004c   Code   RO         2696    i.ebtn_init         ebtn.o
    0x08004e5c   0x08004e5c   0x0000001a   Code   RO         2700    i.ebtn_process      ebtn.o
    0x08004e76   0x08004e76   0x00000038   Code   RO         2701    i.ebtn_process_btn  ebtn.o
    0x08004eae   0x08004eae   0x00000074   Code   RO         2702    i.ebtn_process_btn_combo  ebtn.o
    0x08004f22   0x08004f22   0x00000002   PAD
    0x08004f24   0x08004f24   0x000001c0   Code   RO         2703    i.ebtn_process_with_curr_state  ebtn.o
    0x080050e4   0x080050e4   0x0000000c   Code   RO         2705    i.ebtn_set_config   ebtn.o
    0x080050f0   0x080050f0   0x00000006   Code   RO         2706    i.ebtn_timer_sub    ebtn.o
    0x080050f6   0x080050f6   0x00000002   PAD
    0x080050f8   0x080050f8   0x00000024   Code   RO         2390    i.key_read          key_app.o
    0x0800511c   0x0800511c   0x0000004c   Code   RO         2391    i.key_task          key_app.o
    0x08005168   0x08005168   0x00000078   Code   RO         2460    i.led_disp          led_app.o
    0x080051e0   0x080051e0   0x00000010   Code   RO         2461    i.led_task          led_app.o
    0x080051f0   0x080051f0   0x00000068   Code   RO           15    i.main              main.o
    0x08005258   0x08005258   0x0000003a   Code   RO         2584    i.my_printf         usart_app.o
    0x08005292   0x08005292   0x00000002   PAD
    0x08005294   0x08005294   0x00000064   Code   RO         2632    i.oled_task         oled_app.o
    0x080052f8   0x080052f8   0x00000080   Code   RO         2532    i.prv_btn_event     btn_app.o
    0x08005378   0x08005378   0x0000003c   Code   RO         2533    i.prv_btn_get_state  btn_app.o
    0x080053b4   0x080053b4   0x000001e8   Code   RO         2707    i.prv_process_btn   ebtn.o
    0x0800559c   0x0800559c   0x0000003c   Code   RO         2864    i.rt_ringbuffer_data_len  ringbuffer.o
    0x080055d8   0x080055d8   0x000000ec   Code   RO         2865    i.rt_ringbuffer_get  ringbuffer.o
    0x080056c4   0x080056c4   0x00000094   Code   RO         2867    i.rt_ringbuffer_init  ringbuffer.o
    0x08005758   0x08005758   0x000000f0   Code   RO         2869    i.rt_ringbuffer_put  ringbuffer.o
    0x08005848   0x08005848   0x0000002a   Code   RO         2874    i.rt_ringbuffer_status  ringbuffer.o
    0x08005872   0x08005872   0x00000002   PAD
    0x08005874   0x08005874   0x0000000c   Code   RO         2497    i.scheduler_init    scheduler.o
    0x08005880   0x08005880   0x00000058   Code   RO         2498    i.scheduler_run     scheduler.o
    0x080058d8   0x080058d8   0x0000001a   Code   RO         3927    i.u8g2_ClearBuffer  u8g2_buffer.o
    0x080058f2   0x080058f2   0x00000046   Code   RO         4027    i.u8g2_DrawCircle   u8g2_circle.o
    0x08005938   0x08005938   0x00000062   Code   RO         4136    i.u8g2_DrawGlyph    u8g2_font.o
    0x0800599a   0x0800599a   0x000000dc   Code   RO         4492    i.u8g2_DrawHVLine   u8g2_hvline.o
    0x08005a76   0x08005a76   0x00000044   Code   RO         4493    i.u8g2_DrawPixel    u8g2_hvline.o
    0x08005aba   0x08005aba   0x00000002   PAD
    0x08005abc   0x08005abc   0x00000024   Code   RO         4139    i.u8g2_DrawStr      u8g2_font.o
    0x08005ae0   0x08005ae0   0x0000003a   Code   RO         4550    i.u8g2_IsIntersection  u8g2_intersection.o
    0x08005b1a   0x08005b1a   0x00000012   Code   RO         3930    i.u8g2_SendBuffer   u8g2_buffer.o
    0x08005b2c   0x08005b2c   0x00000010   Code   RO         4495    i.u8g2_SetDrawColor  u8g2_hvline.o
    0x08005b3c   0x08005b3c   0x0000000c   Code   RO         4161    i.u8g2_SetFontPosBaseline  u8g2_font.o
    0x08005b48   0x08005b48   0x00000020   Code   RO         4765    i.u8g2_SetMaxClipWindow  u8g2_setup.o
    0x08005b68   0x08005b68   0x0000005a   Code   RO         4766    i.u8g2_SetupBuffer  u8g2_setup.o
    0x08005bc2   0x08005bc2   0x00000002   PAD
    0x08005bc4   0x08005bc4   0x00000044   Code   RO         4122    i.u8g2_Setup_ssd1315_i2c_128x64_noname_f  u8g2_d_setup.o
    0x08005c08   0x08005c08   0x0000002a   Code   RO         4169    i.u8g2_add_vector_x  u8g2_font.o
    0x08005c32   0x08005c32   0x0000002a   Code   RO         4170    i.u8g2_add_vector_y  u8g2_font.o
    0x08005c5c   0x08005c5c   0x0000007a   Code   RO         4768    i.u8g2_apply_clip_window  u8g2_setup.o
    0x08005cd6   0x08005cd6   0x0000004c   Code   RO         4496    i.u8g2_clip_intersection2  u8g2_hvline.o
    0x08005d22   0x08005d22   0x00000080   Code   RO         4031    i.u8g2_draw_circle  u8g2_circle.o
    0x08005da2   0x08005da2   0x0000009c   Code   RO         4032    i.u8g2_draw_circle_section  u8g2_circle.o
    0x08005e3e   0x08005e3e   0x0000002c   Code   RO         4497    i.u8g2_draw_hv_line_2dir  u8g2_hvline.o
    0x08005e6a   0x08005e6a   0x0000003c   Code   RO         4769    i.u8g2_draw_l90_mirrorr_r0  u8g2_setup.o
    0x08005ea6   0x08005ea6   0x00000024   Code   RO         4770    i.u8g2_draw_l90_r0  u8g2_setup.o
    0x08005eca   0x08005eca   0x00000044   Code   RO         4771    i.u8g2_draw_l90_r1  u8g2_setup.o
    0x08005f0e   0x08005f0e   0x00000056   Code   RO         4772    i.u8g2_draw_l90_r2  u8g2_setup.o
    0x08005f64   0x08005f64   0x00000050   Code   RO         4773    i.u8g2_draw_l90_r3  u8g2_setup.o
    0x08005fb4   0x08005fb4   0x0000003c   Code   RO         4774    i.u8g2_draw_mirror_vertical_r0  u8g2_setup.o
    0x08005ff0   0x08005ff0   0x00000090   Code   RO         4171    i.u8g2_draw_string  u8g2_font.o
    0x08006080   0x08006080   0x00000006   Code   RO         4178    i.u8g2_font_calc_vref_font  u8g2_font.o
    0x08006086   0x08006086   0x00000024   Code   RO         4180    i.u8g2_font_decode_get_signed_bits  u8g2_font.o
    0x080060aa   0x080060aa   0x00000042   Code   RO         4181    i.u8g2_font_decode_get_unsigned_bits  u8g2_font.o
    0x080060ec   0x080060ec   0x00000182   Code   RO         4182    i.u8g2_font_decode_glyph  u8g2_font.o
    0x0800626e   0x0800626e   0x000000b0   Code   RO         4183    i.u8g2_font_decode_len  u8g2_font.o
    0x0800631e   0x0800631e   0x0000003a   Code   RO         4184    i.u8g2_font_draw_glyph  u8g2_font.o
    0x08006358   0x08006358   0x00000092   Code   RO         4186    i.u8g2_font_get_glyph_data  u8g2_font.o
    0x080063ea   0x080063ea   0x00000018   Code   RO         4187    i.u8g2_font_get_word  u8g2_font.o
    0x08006402   0x08006402   0x00000040   Code   RO         4188    i.u8g2_font_setup_decode  u8g2_font.o
    0x08006442   0x08006442   0x000000a0   Code   RO         2633    i.u8g2_gpio_and_delay_stm32  oled_app.o
    0x080064e2   0x080064e2   0x00000030   Code   RO         4551    i.u8g2_is_intersection_decision_tree  u8g2_intersection.o
    0x08006512   0x08006512   0x000000ee   Code   RO         4599    i.u8g2_ll_hvline_vertical_top_lsb  u8g2_ll_hvline.o
    0x08006600   0x08006600   0x00000010   Code   RO         4109    i.u8g2_m_16_8_f     u8g2_d_memory.o
    0x08006610   0x08006610   0x00000036   Code   RO         3938    i.u8g2_send_buffer  u8g2_buffer.o
    0x08006646   0x08006646   0x00000036   Code   RO         3939    i.u8g2_send_tile_row  u8g2_buffer.o
    0x0800667c   0x0800667c   0x00000060   Code   RO         4775    i.u8g2_update_dimension_common  u8g2_setup.o
    0x080066dc   0x080066dc   0x0000000c   Code   RO         4776    i.u8g2_update_dimension_r0  u8g2_setup.o
    0x080066e8   0x080066e8   0x0000001c   Code   RO         4777    i.u8g2_update_dimension_r1  u8g2_setup.o
    0x08006704   0x08006704   0x0000000c   Code   RO         4778    i.u8g2_update_dimension_r2  u8g2_setup.o
    0x08006710   0x08006710   0x0000001c   Code   RO         4779    i.u8g2_update_dimension_r3  u8g2_setup.o
    0x0800672c   0x0800672c   0x00000020   Code   RO         4780    i.u8g2_update_page_win_r0  u8g2_setup.o
    0x0800674c   0x0800674c   0x00000020   Code   RO         4781    i.u8g2_update_page_win_r1  u8g2_setup.o
    0x0800676c   0x0800676c   0x0000003c   Code   RO         4782    i.u8g2_update_page_win_r2  u8g2_setup.o
    0x080067a8   0x080067a8   0x0000003c   Code   RO         4783    i.u8g2_update_page_win_r3  u8g2_setup.o
    0x080067e4   0x080067e4   0x00000032   Code   RO         5568    i.u8x8_DrawTile     u8x8_display.o
    0x08006816   0x08006816   0x00000012   Code   RO         5570    i.u8x8_InitDisplay  u8x8_display.o
    0x08006828   0x08006828   0x00000012   Code   RO         5572    i.u8x8_RefreshDisplay  u8x8_display.o
    0x0800683a   0x0800683a   0x00000014   Code   RO         5575    i.u8x8_SetPowerSave  u8x8_display.o
    0x0800684e   0x0800684e   0x0000002a   Code   RO         5746    i.u8x8_Setup        u8x8_setup.o
    0x08006878   0x08006878   0x0000002c   Code   RO         5747    i.u8x8_SetupDefaults  u8x8_setup.o
    0x080068a4   0x080068a4   0x00000012   Code   RO         5576    i.u8x8_SetupMemory  u8x8_display.o
    0x080068b6   0x080068b6   0x00000012   Code   RO         5064    i.u8x8_ascii_next   u8x8_8x8.o
    0x080068c8   0x080068c8   0x00000012   Code   RO         5200    i.u8x8_byte_EndTransfer  u8x8_byte.o
    0x080068da   0x080068da   0x00000010   Code   RO         5201    i.u8x8_byte_SendByte  u8x8_byte.o
    0x080068ea   0x080068ea   0x0000001a   Code   RO         5202    i.u8x8_byte_SendBytes  u8x8_byte.o
    0x08006904   0x08006904   0x00000012   Code   RO         5204    i.u8x8_byte_StartTransfer  u8x8_byte.o
    0x08006916   0x08006916   0x00000002   PAD
    0x08006918   0x08006918   0x00000088   Code   RO         2634    i.u8x8_byte_hw_i2c  oled_app.o
    0x080069a0   0x080069a0   0x00000012   Code   RO         5337    i.u8x8_cad_EndTransfer  u8x8_cad.o
    0x080069b2   0x080069b2   0x00000014   Code   RO         5338    i.u8x8_cad_SendArg  u8x8_cad.o
    0x080069c6   0x080069c6   0x00000014   Code   RO         5339    i.u8x8_cad_SendCmd  u8x8_cad.o
    0x080069da   0x080069da   0x0000001a   Code   RO         5340    i.u8x8_cad_SendData  u8x8_cad.o
    0x080069f4   0x080069f4   0x0000007c   Code   RO         5342    i.u8x8_cad_SendSequence  u8x8_cad.o
    0x08006a70   0x08006a70   0x00000012   Code   RO         5343    i.u8x8_cad_StartTransfer  u8x8_cad.o
    0x08006a82   0x08006a82   0x00000002   PAD
    0x08006a84   0x08006a84   0x000000d4   Code   RO         5346    i.u8x8_cad_ssd13xx_fast_i2c  u8x8_cad.o
    0x08006b58   0x08006b58   0x00000060   Code   RO         5577    i.u8x8_d_helper_display_init  u8x8_display.o
    0x08006bb8   0x08006bb8   0x0000000c   Code   RO         5578    i.u8x8_d_helper_display_setup_memory  u8x8_display.o
    0x08006bc4   0x08006bc4   0x00000054   Code   RO         5524    i.u8x8_d_ssd1315_128x64_noname  u8x8_d_ssd1315_128x64_noname.o
    0x08006c18   0x08006c18   0x00000104   Code   RO         5525    i.u8x8_d_ssd1315_generic  u8x8_d_ssd1315_128x64_noname.o
    0x08006d1c   0x08006d1c   0x00000008   Code   RO         5749    i.u8x8_dummy_cb     u8x8_setup.o
    0x08006d24   0x08006d24   0x0000001a   Code   RO         5668    i.u8x8_gpio_call    u8x8_gpio.o
    0x08006d3e   0x08006d3e   0x0000002e   Code   RO         5354    i.u8x8_i2c_data_transfer  u8x8_cad.o
    0x08006d6c   0x08006d6c   0x00000008   Code   RO         5073    i.u8x8_utf8_init    u8x8_8x8.o
    0x08006d74   0x08006d74   0x0000004c   Code   RO         2585    i.uart_task         usart_app.o
    0x08006dc0   0x08006dc0   0x00000012   Data   RO         1161    .constdata          stm32f1xx_hal_rcc.o
    0x08006dd2   0x08006dd2   0x00000018   Data   RO         2355    .constdata          system_stm32f1xx.o
    0x08006dea   0x08006dea   0x00000002   PAD
    0x08006dec   0x08006dec   0x00000011   Data   RO         2462    .constdata          led_app.o
    0x08006dfd   0x08006dfd   0x00000001   PAD
    0x08006dfe   0x08006dfe   0x0000001c   Data   RO         2534    .constdata          btn_app.o
    0x08006e1a   0x08006e1a   0x00000002   PAD
    0x08006e1c   0x08006e1c   0x00000048   Data   RO         4784    .constdata          u8g2_setup.o
    0x08006e64   0x08006e64   0x00000064   Data   RO         5526    .constdata          u8x8_d_ssd1315_128x64_noname.o
    0x08006ec8   0x08006ec8   0x00000020   Data   RO         5969    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08006ee8, Size: 0x00000c68, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08006ee8   0x00000009   Data   RW          997    .data               stm32f1xx_hal.o
    0x20000009   0x08006ef1   0x00000003   PAD
    0x2000000c   0x08006ef4   0x00000004   Data   RW         2356    .data               system_stm32f1xx.o
    0x20000010   0x08006ef8   0x00000004   Data   RW         2392    .data               key_app.o
    0x20000014   0x08006efc   0x00000004   Data   RW         2463    .data               led_app.o
    0x20000018   0x08006f00   0x00000040   Data   RW         2499    .data               scheduler.o
    0x20000058   0x08006f40   0x0000009c   Data   RW         2535    .data               btn_app.o
    0x200000f4   0x08006fdc   0x00000001   Data   RW         2636    .data               oled_app.o
    0x200000f5   0x08006fdd   0x00000016   Data   RW         2966    .data               oled.o
    0x2000010b   0x08006ff3   0x00000006   Data   RW         5356    .data               u8x8_cad.o
    0x20000111   0x08006ff9   0x00000003   PAD
    0x20000114        -       0x00000054   Zero   RW          256    .bss                i2c.o
    0x20000168        -       0x0000008c   Zero   RW          298    .bss                usart.o
    0x200001f4        -       0x0000018c   Zero   RW         2586    .bss                usart_app.o
    0x20000380        -       0x000000b4   Zero   RW         2635    .bss                oled_app.o
    0x20000434        -       0x00000034   Zero   RW         2708    .bss                ebtn.o
    0x20000468        -       0x00000400   Zero   RW         4110    .bss                u8g2_d_memory.o
    0x20000868        -       0x00000400   Zero   RW            1    STACK               startup_stm32f103xe.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       298         56         28        156          0      10228   btn_app.o
        52          4          0          0          0        750   dma.o
      1684         30          0          0         52      19623   ebtn.o
       200         14          0          0          0        947   gpio.o
       192         24          0          0         84       1629   i2c.o
       112         22          0          4          0       3465   key_app.o
       136         18         17          4          0       2144   led_app.o
       212         24          0          0          0     628456   main.o
       220         18          0         22          0       3672   oled.o
       396         50          0          1        180       2630   oled_app.o
       726        178          0          0          0       6035   ringbuffer.o
       100         14          0         64          0       1632   scheduler.o
        36          8        304          0       1024        804   startup_stm32f103xe.o
       192         36          0          9          0       6689   stm32f1xx_hal.o
       304         22          0          0          0      29743   stm32f1xx_hal_cortex.o
      1778         28          0          0          0       6097   stm32f1xx_hal_dma.o
       860         40          0          0          0       3463   stm32f1xx_hal_gpio.o
      6284         74          0          0          0      31715   stm32f1xx_hal_i2c.o
        84          8          0          0          0        902   stm32f1xx_hal_msp.o
      1748         98         18          0          0       6200   stm32f1xx_hal_rcc.o
      2740         22          0          0          0      16868   stm32f1xx_hal_uart.o
        82         18          0          0          0       5366   stm32f1xx_it.o
         2          0         24          4          0       1175   system_stm32f1xx.o
       152          0          0          0          0       2809   u8g2_buffer.o
       354          0          0          0          0       2314   u8g2_circle.o
        16          6          0          0       1024        872   u8g2_d_memory.o
        68         14          0          0          0       1093   u8g2_d_setup.o
      1336         10          0          0          0      11368   u8g2_font.o
       424          0          0          0          0       3832   u8g2_hvline.o
       106          0          0          0          0       1543   u8g2_intersection.o
       238          0          0          0          0       1430   u8g2_ll_hvline.o
       994          0         72          0          0     158723   u8g2_setup.o
        26          0          0          0          0       1063   u8x8_8x8.o
        78          0          0          0          0       2657   u8x8_byte.o
       484         10          0          6          0       8320   u8x8_cad.o
       344         26        100          0          0       2779   u8x8_d_ssd1315_128x64_noname.o
       232          0          0          0          0       4422   u8x8_display.o
        26          0          0          0          0        613   u8x8_gpio.o
        94          6          0          0          0       1894   u8x8_setup.o
       296         36          0          0        140       1801   usart.o
       294         98          0          0        396       3079   usart_app.o

    ----------------------------------------------------------------------
     24036       <USER>        <GROUP>        276       2900    1000845   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        36          0          5          6          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        26          0          0          0          0         80   memcmp.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2260         86          0          0          0        528   printfa.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
       228          0          0          0          0         96   dmul.o

    ----------------------------------------------------------------------
      3756        <USER>          <GROUP>          0          0       1884   Library Totals
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2688        102          0          0          0       1228   mc_w.l
      1066          0          0          0          0        656   mf_w.l

    ----------------------------------------------------------------------
      3756        <USER>          <GROUP>          0          0       1884   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     27792       1114        600        276       2900     990037   Grand Totals
     27792       1114        600        276       2900     990037   ELF Image Totals
     27792       1114        600        276          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                28392 (  27.73kB)
    Total RW  Size (RW Data + ZI Data)              3176 (   3.10kB)
    Total ROM Size (Code + RO Data + RW Data)      28668 (  28.00kB)

==============================================================================

