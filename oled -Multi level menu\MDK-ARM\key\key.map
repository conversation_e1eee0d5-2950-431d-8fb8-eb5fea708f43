Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f103xe.o(RESET) refers to startup_stm32f103xe.o(STACK) for __initial_sp
    startup_stm32f103xe.o(RESET) refers to startup_stm32f103xe.o(.text) for Reset_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler) for DMA1_Channel5_IRQHandler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.I2C1_EV_IRQHandler) for I2C1_EV_IRQHandler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f103xe.o(.text) refers to system_stm32f1xx.o(i.SystemInit) for SystemInit
    startup_stm32f103xe.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to btn_app.o(i.app_ebtn_init) for app_ebtn_init
    main.o(i.main) refers to scheduler.o(i.scheduler_init) for scheduler_init
    main.o(i.main) refers to ringbuffer.o(i.rt_ringbuffer_init) for rt_ringbuffer_init
    main.o(i.main) refers to u8g2_d_setup.o(i.u8g2_Setup_ssd1315_i2c_128x64_noname_f) for u8g2_Setup_ssd1315_i2c_128x64_noname_f
    main.o(i.main) refers to u8x8_display.o(i.u8x8_InitDisplay) for u8x8_InitDisplay
    main.o(i.main) refers to u8x8_display.o(i.u8x8_SetPowerSave) for u8x8_SetPowerSave
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to scheduler.o(i.scheduler_run) for scheduler_run
    main.o(i.main) refers to usart_app.o(.bss) for ringbuffer_pool
    main.o(i.main) refers to oled_app.o(i.u8g2_gpio_and_delay_stm32) for u8g2_gpio_and_delay_stm32
    main.o(i.main) refers to oled_app.o(i.u8x8_byte_hw_i2c) for u8x8_byte_hw_i2c
    main.o(i.main) refers to u8g2_setup.o(.constdata) for u8g2_cb_r0
    main.o(i.main) refers to oled_app.o(.bss) for u8g2
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dma.o(i.MX_DMA_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.MX_I2C1_Init) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for hi2c1
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for hdma_usart1_rx
    usart.o(i.MX_USART1_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for huart1
    usart.o(i.MX_USART1_UART_Init) refers to usart_app.o(.bss) for uart_rx_dma_buffer
    stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f1xx_it.o(i.I2C1_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) for HAL_I2C_EV_IRQHandler
    stm32f1xx_it.o(i.I2C1_EV_IRQHandler) refers to i2c.o(.bss) for hi2c1
    stm32f1xx_it.o(i.SysTick_Handler) refers to stm32f1xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f1xx_it.o(i.USART1_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f1xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f1xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Master_ADD10) for I2C_Master_ADD10
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE) for I2C_SlaveTransmit_TXE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_SlaveTransmit_BTF) for I2C_SlaveTransmit_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE) for I2C_SlaveReceive_RXNE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_SlaveReceive_BTF) for I2C_SlaveReceive_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnSTOPRequestThroughIT) for I2C_WaitOnSTOPRequestThroughIT
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f1xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnSTOPRequestThroughIT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_DeInit) refers to stm32f1xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(.data) for uwTickFreq
    stm32f1xx_hal.o(i.HAL_GetTick) refers to stm32f1xx_hal.o(.data) for uwTick
    stm32f1xx_hal.o(i.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.data) for uwTickFreq
    stm32f1xx_hal.o(i.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal.o(i.HAL_IncTick) refers to stm32f1xx_hal.o(.data) for uwTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for uwTickFreq
    stm32f1xx_hal.o(i.HAL_InitTick) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.data) for uwTickFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.constdata) for AHBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to stm32f1xx_hal_rcc.o(.constdata) for aPLLMULFactorTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal_rcc.o(i.RCC_Delay) for RCC_Delay
    stm32f1xx_hal_rcc.o(i.RCC_Delay) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc_ex.o(.constdata) for aPLLMULFactorTable
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping) refers to stm32f1xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe) for PWR_OverloadWfe
    stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to llushr.o(.text) for __aeabi_llsr
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData) for FLASH_OB_ProgramData
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.constdata) for AHBPrescTable
    key_app.o(i.key_read) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_app.o(i.key_task) refers to key_app.o(i.key_read) for key_read
    key_app.o(i.key_task) refers to key_app.o(.data) for key_val
    led_app.o(i.led_disp) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    led_app.o(i.led_disp) refers to led_app.o(.data) for temp_old
    led_app.o(i.led_disp) refers to led_app.o(.constdata) for led_pin_configs
    led_app.o(i.led_task) refers to led_app.o(i.led_disp) for led_disp
    led_app.o(i.led_task) refers to led_app.o(.data) for ucLed
    scheduler.o(i.scheduler_init) refers to scheduler.o(.data) for task_num
    scheduler.o(i.scheduler_run) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.scheduler_run) refers to scheduler.o(.data) for scheduler_task
    scheduler.o(.data) refers to led_app.o(i.led_task) for led_task
    scheduler.o(.data) refers to key_app.o(i.key_task) for key_task
    scheduler.o(.data) refers to btn_app.o(i.btn_task) for btn_task
    scheduler.o(.data) refers to usart_app.o(i.uart_task) for uart_task
    scheduler.o(.data) refers to oled_app.o(i.oled_task) for oled_task
    btn_app.o(i.app_ebtn_init) refers to ebtn.o(i.ebtn_init) for ebtn_init
    btn_app.o(i.app_ebtn_init) refers to ebtn.o(i.ebtn_set_config) for ebtn_set_config
    btn_app.o(i.app_ebtn_init) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    btn_app.o(i.app_ebtn_init) refers to ebtn.o(i.ebtn_combo_btn_add_btn_by_idx) for ebtn_combo_btn_add_btn_by_idx
    btn_app.o(i.app_ebtn_init) refers to btn_app.o(i.prv_btn_event) for prv_btn_event
    btn_app.o(i.app_ebtn_init) refers to btn_app.o(i.prv_btn_get_state) for prv_btn_get_state
    btn_app.o(i.app_ebtn_init) refers to btn_app.o(.data) for btns_combo
    btn_app.o(i.btn_task) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    btn_app.o(i.btn_task) refers to ebtn.o(i.ebtn_process) for ebtn_process
    btn_app.o(i.prv_btn_event) refers to usart_app.o(i.my_printf) for my_printf
    btn_app.o(i.prv_btn_event) refers to led_app.o(.data) for ucLed
    btn_app.o(i.prv_btn_event) refers to usart.o(.bss) for huart1
    btn_app.o(i.prv_btn_get_state) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    btn_app.o(.data) refers to btn_app.o(.constdata) for default_ebtn_param
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(i.rt_ringbuffer_put) for rt_ringbuffer_put
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to memseta.o(.text) for __aeabi_memclr4
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to usart_app.o(.bss) for uart_rx_dma_buffer
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for huart1
    usart_app.o(i.__aeabi_assert) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.__aeabi_assert) refers to usart.o(.bss) for huart1
    usart_app.o(i.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    usart_app.o(i.my_printf) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart_app.o(i.uart_task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    usart_app.o(i.uart_task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    usart_app.o(i.uart_task) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.uart_task) refers to memseta.o(.text) for __aeabi_memclr4
    usart_app.o(i.uart_task) refers to usart_app.o(.bss) for uart_ringbuffer
    usart_app.o(i.uart_task) refers to usart.o(.bss) for huart1
    oled_app.o(i.oled_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    oled_app.o(i.oled_printf) refers to oled.o(i.OLED_ShowStr) for OLED_ShowStr
    oled_app.o(i.oled_task) refers to u8g2_hvline.o(i.u8g2_SetDrawColor) for u8g2_SetDrawColor
    oled_app.o(i.oled_task) refers to u8g2_font.o(i.u8g2_SetFont) for u8g2_SetFont
    oled_app.o(i.oled_task) refers to u8g2_buffer.o(i.u8g2_ClearBuffer) for u8g2_ClearBuffer
    oled_app.o(i.oled_task) refers to u8g2_font.o(i.u8g2_DrawStr) for u8g2_DrawStr
    oled_app.o(i.oled_task) refers to u8g2_circle.o(i.u8g2_DrawCircle) for u8g2_DrawCircle
    oled_app.o(i.oled_task) refers to u8g2_buffer.o(i.u8g2_SendBuffer) for u8g2_SendBuffer
    oled_app.o(i.oled_task) refers to oled_app.o(.bss) for u8g2
    oled_app.o(i.oled_task) refers to u8g2_fonts.o(.constdata) for u8g2_font_ncenB08_tr
    oled_app.o(i.u8g2_gpio_and_delay_stm32) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled_app.o(i.u8x8_byte_hw_i2c) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    oled_app.o(i.u8x8_byte_hw_i2c) refers to oled_app.o(.data) for buf_idx
    oled_app.o(i.u8x8_byte_hw_i2c) refers to oled_app.o(.bss) for buffer
    oled_app.o(i.u8x8_byte_hw_i2c) refers to i2c.o(.bss) for hi2c1
    ebtn.o(i.bit_array_cmp) refers to memcmp.o(.text) for memcmp
    ebtn.o(i.ebtn_combo_btn_add_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_combo_btn_add_btn) refers to ebtn.o(i.ebtn_combo_btn_add_btn_by_idx) for ebtn_combo_btn_add_btn_by_idx
    ebtn.o(i.ebtn_combo_btn_remove_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_combo_btn_remove_btn) refers to ebtn.o(i.ebtn_combo_btn_remove_btn_by_idx) for ebtn_combo_btn_remove_btn_by_idx
    ebtn.o(i.ebtn_combo_register) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_btn_by_key_id) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_btn_index_by_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_get_btn_index_by_btn_dyn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_get_btn_index_by_key_id) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_config) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_current_state) refers to ebtn.o(i.bit_array_assign) for bit_array_assign
    ebtn.o(i.ebtn_get_current_state) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_total_btn_cnt) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_init) refers to memseta.o(.text) for __aeabi_memclr4
    ebtn.o(i.ebtn_init) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_is_in_process) refers to ebtn.o(i.ebtn_is_btn_in_process) for ebtn_is_btn_in_process
    ebtn.o(i.ebtn_is_in_process) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_process) refers to ebtn.o(i.ebtn_get_current_state) for ebtn_get_current_state
    ebtn.o(i.ebtn_process) refers to ebtn.o(i.ebtn_process_with_curr_state) for ebtn_process_with_curr_state
    ebtn.o(i.ebtn_process_btn) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.ebtn_process_btn) refers to ebtn.o(i.prv_process_btn) for prv_process_btn
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_num_bits_set) for bit_array_num_bits_set
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_and) for bit_array_and
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_cmp) for bit_array_cmp
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.prv_process_btn) for prv_process_btn
    ebtn.o(i.ebtn_process_with_curr_state) refers to memseta.o(.text) for __aeabi_memclr4
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_and) for bit_array_and
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_cmp) for bit_array_cmp
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_or) for bit_array_or
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.ebtn_process_btn) for ebtn_process_btn
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.ebtn_process_btn_combo) for ebtn_process_btn_combo
    ebtn.o(i.ebtn_process_with_curr_state) refers to memcpya.o(.text) for __aeabi_memcpy4
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_register) refers to ebtn.o(i.ebtn_get_total_btn_cnt) for ebtn_get_total_btn_cnt
    ebtn.o(i.ebtn_register) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_set_config) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.prv_process_btn) refers to ebtn.o(i.ebtn_timer_sub) for ebtn_timer_sub
    ebtn.o(i.prv_process_btn) refers to ebtn.o(.bss) for ebtn_default
    ringbuffer.o(i.rt_ringbuffer_data_len) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_get) refers to usart_app.o(i.__aeabi_assert) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_get) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_get) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to usart_app.o(i.__aeabi_assert) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_init) refers to usart_app.o(i.__aeabi_assert) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_peek) refers to usart_app.o(i.__aeabi_assert) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_peek) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to usart_app.o(i.__aeabi_assert) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_put) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to usart_app.o(i.__aeabi_assert) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to usart_app.o(i.__aeabi_assert) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to usart_app.o(i.__aeabi_assert) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_reset) refers to usart_app.o(i.__aeabi_assert) for __aeabi_assert
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_Init) refers to oled.o(.data) for initcmd1
    oled.o(i.OLED_Set_Position) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for F8X16
    oled.o(i.OLED_ShowFloat) refers to cfcmple.o(.text) for __aeabi_cfcmple
    oled.o(i.OLED_ShowFloat) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowFloat) refers to fadd.o(.text) for __aeabi_fsub
    oled.o(i.OLED_ShowFloat) refers to ffixui.o(.text) for __aeabi_f2uiz
    oled.o(i.OLED_ShowFloat) refers to ffltui.o(.text) for __aeabi_ui2f
    oled.o(i.OLED_ShowFloat) refers to fmul.o(.text) for __aeabi_fmul
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHanzi) refers to oled.o(.constdata) for Hzk
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHzbig) refers to oled.o(.constdata) for Hzb
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowStr) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Write_cmd) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_Write_cmd) refers to i2c.o(.bss) for hi2c1
    oled.o(i.OLED_Write_data) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_Write_data) refers to i2c.o(.bss) for hi2c1
    mui.o(i.mui_Draw) refers to mui.o(i.mui_loop_over_form) for mui_loop_over_form
    mui.o(i.mui_Draw) refers to mui.o(i.mui_task_draw) for mui_task_draw
    mui.o(i.mui_EnterForm) refers to mui.o(i.mui_LeaveForm) for mui_LeaveForm
    mui.o(i.mui_EnterForm) refers to mui.o(i.mui_loop_over_form) for mui_loop_over_form
    mui.o(i.mui_EnterForm) refers to mui.o(i.mui_NextField) for mui_NextField
    mui.o(i.mui_EnterForm) refers to mui.o(i.mui_send_cursor_enter_msg) for mui_send_cursor_enter_msg
    mui.o(i.mui_EnterForm) refers to mui.o(i.mui_task_form_start) for mui_task_form_start
    mui.o(i.mui_EnterForm) refers to mui.o(i.mui_task_find_first_cursor_uif) for mui_task_find_first_cursor_uif
    mui.o(i.mui_GetCurrentCursorFocusPosition) refers to mui.o(i.mui_loop_over_form) for mui_loop_over_form
    mui.o(i.mui_GetCurrentCursorFocusPosition) refers to mui.o(i.mui_task_get_current_cursor_focus_position) for mui_task_get_current_cursor_focus_position
    mui.o(i.mui_GetCurrentFormId) refers to mui.o(i.mui_get_fds_char) for mui_get_fds_char
    mui.o(i.mui_GetSelectableFieldOptionCnt) refers to mui.o(i.mui_fds_get_token_cnt) for mui_fds_get_token_cnt
    mui.o(i.mui_GetSelectableFieldTextOption) refers to mui.o(i.mui_fds_get_nth_token) for mui_fds_get_nth_token
    mui.o(i.mui_GotoForm) refers to mui.o(i.mui_find_form) for mui_find_form
    mui.o(i.mui_GotoForm) refers to mui.o(i.mui_EnterForm) for mui_EnterForm
    mui.o(i.mui_GotoFormAutoCursorPosition) refers to mui.o(i.mui_GotoForm) for mui_GotoForm
    mui.o(i.mui_Init) refers to memseta.o(.text) for __aeabi_memclr4
    mui.o(i.mui_LeaveForm) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_LeaveForm) refers to mui.o(i.mui_loop_over_form) for mui_loop_over_form
    mui.o(i.mui_LeaveForm) refers to mui.o(i.mui_task_form_end) for mui_task_form_end
    mui.o(i.mui_NextField) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_NextField) refers to mui.o(i.mui_next_field) for mui_next_field
    mui.o(i.mui_NextField) refers to mui.o(i.mui_send_cursor_enter_msg) for mui_send_cursor_enter_msg
    mui.o(i.mui_PrevField) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_PrevField) refers to mui.o(i.mui_loop_over_form) for mui_loop_over_form
    mui.o(i.mui_PrevField) refers to mui.o(i.mui_send_cursor_enter_msg) for mui_send_cursor_enter_msg
    mui.o(i.mui_PrevField) refers to mui.o(i.mui_task_find_prev_cursor_uif) for mui_task_find_prev_cursor_uif
    mui.o(i.mui_PrevField) refers to mui.o(i.mui_task_find_last_cursor_uif) for mui_task_find_last_cursor_uif
    mui.o(i.mui_RestoreForm) refers to mui.o(i.mui_GotoForm) for mui_GotoForm
    mui.o(i.mui_SaveCursorPosition) refers to mui.o(i.mui_get_fds_char) for mui_get_fds_char
    mui.o(i.mui_SaveForm) refers to mui.o(i.mui_GetCurrentCursorFocusPosition) for mui_GetCurrentCursorFocusPosition
    mui.o(i.mui_SaveForm) refers to mui.o(i.mui_SaveFormWithCursorPosition) for mui_SaveFormWithCursorPosition
    mui.o(i.mui_SaveFormWithCursorPosition) refers to mui.o(i.mui_get_fds_char) for mui_get_fds_char
    mui.o(i.mui_SendSelect) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_SendSelectWithExecuteOnSelectFieldSearch) refers to mui.o(i.mui_loop_over_form) for mui_loop_over_form
    mui.o(i.mui_SendSelectWithExecuteOnSelectFieldSearch) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_SendSelectWithExecuteOnSelectFieldSearch) refers to mui.o(i.mui_send_cursor_enter_msg) for mui_send_cursor_enter_msg
    mui.o(i.mui_SendSelectWithExecuteOnSelectFieldSearch) refers to mui.o(i.mui_task_find_execute_on_select_field) for mui_task_find_execute_on_select_field
    mui.o(i.mui_SendValueDecrement) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_SendValueIncrement) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_fds_first_token) refers to mui.o(i.mui_fds_get_cmd_size_without_text) for mui_fds_get_cmd_size_without_text
    mui.o(i.mui_fds_first_token) refers to mui.o(i.mui_get_fds_char) for mui_get_fds_char
    mui.o(i.mui_fds_first_token) refers to mui.o(i.mui_fds_next_token) for mui_fds_next_token
    mui.o(i.mui_fds_get_cmd_size) refers to mui.o(i.mui_fds_get_cmd_size_without_text) for mui_fds_get_cmd_size_without_text
    mui.o(i.mui_fds_get_cmd_size) refers to mui.o(i.mui_get_fds_char) for mui_get_fds_char
    mui.o(i.mui_fds_get_cmd_size) refers to mui.o(i.mui_fds_parse_text) for mui_fds_parse_text
    mui.o(i.mui_fds_get_cmd_size_without_text) refers to mui.o(i.mui_get_fds_char) for mui_get_fds_char
    mui.o(i.mui_fds_get_nth_token) refers to mui.o(i.mui_fds_first_token) for mui_fds_first_token
    mui.o(i.mui_fds_get_nth_token) refers to mui.o(i.mui_fds_next_token) for mui_fds_next_token
    mui.o(i.mui_fds_get_token_cnt) refers to mui.o(i.mui_fds_first_token) for mui_fds_first_token
    mui.o(i.mui_fds_get_token_cnt) refers to mui.o(i.mui_fds_next_token) for mui_fds_next_token
    mui.o(i.mui_fds_next_token) refers to mui.o(i.mui_get_fds_char) for mui_get_fds_char
    mui.o(i.mui_fds_parse_text) refers to mui.o(i.mui_get_fds_char) for mui_get_fds_char
    mui.o(i.mui_find_form) refers to mui.o(i.mui_get_fds_char) for mui_get_fds_char
    mui.o(i.mui_find_form) refers to mui.o(i.mui_fds_get_cmd_size) for mui_fds_get_cmd_size
    mui.o(i.mui_inner_loop_over_form) refers to mui.o(i.mui_fds_get_cmd_size) for mui_fds_get_cmd_size
    mui.o(i.mui_inner_loop_over_form) refers to mui.o(i.mui_get_fds_char) for mui_get_fds_char
    mui.o(i.mui_inner_loop_over_form) refers to mui.o(i.mui_prepare_current_field) for mui_prepare_current_field
    mui.o(i.mui_loop_over_form) refers to mui.o(i.mui_inner_loop_over_form) for mui_inner_loop_over_form
    mui.o(i.mui_next_field) refers to mui.o(i.mui_loop_over_form) for mui_loop_over_form
    mui.o(i.mui_next_field) refers to mui.o(i.mui_task_find_next_cursor_uif) for mui_task_find_next_cursor_uif
    mui.o(i.mui_next_field) refers to mui.o(i.mui_task_find_first_cursor_uif) for mui_task_find_first_cursor_uif
    mui.o(i.mui_prepare_current_field) refers to mui.o(i.mui_fds_get_cmd_size) for mui_fds_get_cmd_size
    mui.o(i.mui_prepare_current_field) refers to mui.o(i.mui_get_fds_char) for mui_get_fds_char
    mui.o(i.mui_prepare_current_field) refers to mui.o(i.mui_find_uif) for mui_find_uif
    mui.o(i.mui_send_cursor_enter_msg) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_send_cursor_msg) refers to mui.o(i.mui_prepare_current_field) for mui_prepare_current_field
    mui.o(i.mui_task_find_first_cursor_uif) refers to mui.o(i.mui_uif_is_cursor_selectable) for mui_uif_is_cursor_selectable
    mui.o(i.mui_task_find_last_cursor_uif) refers to mui.o(i.mui_uif_is_cursor_selectable) for mui_uif_is_cursor_selectable
    mui.o(i.mui_task_find_next_cursor_uif) refers to mui.o(i.mui_uif_is_cursor_selectable) for mui_uif_is_cursor_selectable
    mui.o(i.mui_task_find_prev_cursor_uif) refers to mui.o(i.mui_uif_is_cursor_selectable) for mui_uif_is_cursor_selectable
    mui.o(i.mui_task_get_current_cursor_focus_position) refers to mui.o(i.mui_uif_is_cursor_selectable) for mui_uif_is_cursor_selectable
    mui_u8g2.o(i.mui_get_x) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_hline) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_hline) refers to mui_u8g2.o(i.mui_get_y) for mui_get_y
    mui_u8g2.o(i.mui_hline) refers to u8g2_hvline.o(i.u8g2_DrawHLine) for u8g2_DrawHLine
    mui_u8g2.o(i.mui_u8g2_btn_back_w1_fi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_btn_back_w1_fi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_btn_back_w1_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_fi) for mui_u8g2_draw_button_fi
    mui_u8g2.o(i.mui_u8g2_btn_back_w1_fi) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_btn_back_w1_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_btn_back_w1_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_btn_back_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_btn_back_w1_pi) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_btn_back_w2_fi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_btn_back_w2_fi) refers to mui_u8g2.o(i.mui_u8g2_get_fi_flags) for mui_u8g2_get_fi_flags
    mui_u8g2.o(i.mui_u8g2_btn_back_w2_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_back_w2_fi) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_btn_back_w2_if) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_btn_back_w2_if) refers to mui_u8g2.o(i.mui_u8g2_get_if_flags) for mui_u8g2_get_if_flags
    mui_u8g2.o(i.mui_u8g2_btn_back_w2_if) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_back_w2_if) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_btn_back_wm_fi) refers to mui_u8g2.o(i.mui_u8g2_get_fi_flags) for mui_u8g2_get_fi_flags
    mui_u8g2.o(i.mui_u8g2_btn_back_wm_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_back_wm_fi) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_btn_back_wm_if) refers to mui_u8g2.o(i.mui_u8g2_get_if_flags) for mui_u8g2_get_if_flags
    mui_u8g2.o(i.mui_u8g2_btn_back_wm_if) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_back_wm_if) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_btn_exit_wm_fi) refers to mui_u8g2.o(i.mui_u8g2_get_fi_flags) for mui_u8g2_get_fi_flags
    mui_u8g2.o(i.mui_u8g2_btn_exit_wm_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_exit_wm_fi) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_btn_exit_wm_fi) refers to mui.o(i.mui_LeaveForm) for mui_LeaveForm
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_fi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_fi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_fi) for mui_u8g2_draw_button_fi
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_fi) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_fi) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_pi) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_pi) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_fi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_fi) refers to mui_u8g2.o(i.mui_u8g2_get_fi_flags) for mui_u8g2_get_fi_flags
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_fi) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_fi) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_if) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_if) refers to mui_u8g2.o(i.mui_u8g2_get_if_flags) for mui_u8g2_get_if_flags
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_if) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_if) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_if) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_fi) refers to mui_u8g2.o(i.mui_u8g2_get_fi_flags) for mui_u8g2_get_fi_flags
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_fi) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_fi) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_if) refers to mui_u8g2.o(i.mui_u8g2_get_if_flags) for mui_u8g2_get_if_flags
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_if) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_if) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_if) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_draw_button_fi) refers to mui_u8g2.o(i.mui_u8g2_get_fi_flags) for mui_u8g2_get_fi_flags
    mui_u8g2.o(i.mui_u8g2_draw_button_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_draw_button_if) refers to mui_u8g2.o(i.mui_u8g2_get_if_flags) for mui_u8g2_get_if_flags
    mui_u8g2.o(i.mui_u8g2_draw_button_if) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_draw_button_pf) refers to mui_u8g2.o(i.mui_u8g2_get_pf_flags) for mui_u8g2_get_pf_flags
    mui_u8g2.o(i.mui_u8g2_draw_button_pf) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_draw_button_pi) refers to mui_u8g2.o(i.mui_u8g2_get_pi_flags) for mui_u8g2_get_pi_flags
    mui_u8g2.o(i.mui_u8g2_draw_button_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_draw_button_utf) refers to mui_u8g2.o(i.mui_get_y) for mui_get_y
    mui_u8g2.o(i.mui_u8g2_draw_button_utf) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_draw_button_utf) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_draw_button_utf) refers to u8g2_button.o(i.u8g2_DrawButtonUTF8) for u8g2_DrawButtonUTF8
    mui_u8g2.o(i.mui_u8g2_draw_text) refers to mui_u8g2.o(i.mui_get_y) for mui_get_y
    mui_u8g2.o(i.mui_u8g2_draw_text) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_draw_text) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_draw_text) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui.o(i.mui_GetSelectableFieldTextOption) for mui_GetSelectableFieldTextOption
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pf) for mui_u8g2_draw_button_pf
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui.o(i.mui_SaveFormWithCursorPosition) for mui_SaveFormWithCursorPosition
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui.o(i.mui_SaveCursorPosition) for mui_SaveCursorPosition
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) for mui_u8g2_u8_opt_child_mse_common
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui.o(i.mui_GetSelectableFieldTextOption) for mui_GetSelectableFieldTextOption
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui.o(i.mui_SaveFormWithCursorPosition) for mui_SaveFormWithCursorPosition
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui.o(i.mui_SaveCursorPosition) for mui_SaveCursorPosition
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) for mui_u8g2_u8_opt_child_mse_common
    mui_u8g2.o(i.mui_u8g2_s8_min_max_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pf) for mui_u8g2_s8_vmm_draw_wm_pf
    mui_u8g2.o(i.mui_u8g2_s8_min_max_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pi) for mui_u8g2_s8_vmm_draw_wm_pi
    mui_u8g2.o(i.mui_u8g2_s8_min_max_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pf) for mui_u8g2_s8_vmm_draw_wm_pf
    mui_u8g2.o(i.mui_u8g2_s8_min_max_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pi) for mui_u8g2_s8_vmm_draw_wm_pi
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pf) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pf) refers to u8x8_u8toa.o(i.u8x8_s8toa) for u8x8_s8toa
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pf) refers to u8g2_font.o(i.u8g2_GetStrWidth) for u8g2_GetStrWidth
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pf) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pf) for mui_u8g2_draw_button_pf
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pi) refers to u8x8_u8toa.o(i.u8x8_s8toa) for u8x8_s8toa
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pi) refers to u8g2_font.o(i.u8g2_GetStrWidth) for u8g2_GetStrWidth
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pi) refers to mui_u8g2.o(.constdata) for .constdata
    mui_u8g2.o(i.mui_u8g2_set_font_style_function) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_set_font_style_function) refers to u8g2_font.o(i.u8g2_SetFont) for u8g2_SetFont
    mui_u8g2.o(i.mui_u8g2_u16_list_child_mse_common) refers to mui_u8g2.o(i.mui_u8g2_handle_scroll_next_prev_events) for mui_u8g2_handle_scroll_next_prev_events
    mui_u8g2.o(i.mui_u8g2_u16_list_child_mse_common) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_u16_list_child_w1_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_u16_list_child_w1_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u16_list_child_w1_pi) refers to mui_u8g2.o(i.mui_get_y) for mui_get_y
    mui_u8g2.o(i.mui_u8g2_u16_list_child_w1_pi) refers to mui_u8g2.o(i.u8g2_DrawValueMark) for u8g2_DrawValueMark
    mui_u8g2.o(i.mui_u8g2_u16_list_child_w1_pi) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    mui_u8g2.o(i.mui_u8g2_u16_list_child_w1_pi) refers to u8g2_button.o(i.u8g2_DrawButtonFrame) for u8g2_DrawButtonFrame
    mui_u8g2.o(i.mui_u8g2_u16_list_child_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_u16_list_child_mse_common) for mui_u8g2_u16_list_child_mse_common
    mui_u8g2.o(i.mui_u8g2_u16_list_goto_w1_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_u16_list_goto_w1_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u16_list_goto_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u16_list_goto_w1_pi) refers to mui.o(i.mui_SaveFormWithCursorPosition) for mui_SaveFormWithCursorPosition
    mui_u8g2.o(i.mui_u8g2_u16_list_goto_w1_pi) refers to mui.o(i.mui_SaveCursorPosition) for mui_SaveCursorPosition
    mui_u8g2.o(i.mui_u8g2_u16_list_goto_w1_pi) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_u16_list_goto_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_u16_list_child_mse_common) for mui_u8g2_u16_list_child_mse_common
    mui_u8g2.o(i.mui_u8g2_u16_list_line_wa_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u16_list_line_wa_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u16_list_parent_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u16_list_parent_wm_pi) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_u16_list_parent_wm_pi) refers to mui.o(i.mui_GotoForm) for mui_GotoForm
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to mui_u8g2.o(i.mui_get_y) for mui_get_y
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to u8g2_box.o(i.u8g2_DrawFrame) for u8g2_DrawFrame
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to u8g2_box.o(i.u8g2_DrawBox) for u8g2_DrawBox
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to u8x8_u8toa.o(i.u8x8_u8toa) for u8x8_u8toa
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to u8g2_font.o(i.u8g2_DrawStr) for u8g2_DrawStr
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to u8g2_font.o(i.u8g2_GetStrWidth) for u8g2_GetStrWidth
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to u8g2_button.o(i.u8g2_DrawButtonFrame) for u8g2_DrawButtonFrame
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_get_pf_flags) for mui_u8g2_get_pf_flags
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mse_msg_handler) for mui_u8g2_u8_bar_mse_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_get_pi_flags) for mui_u8g2_get_pi_flags
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mse_msg_handler) for mui_u8g2_u8_bar_mse_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_get_pf_flags) for mui_u8g2_get_pf_flags
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mud_msg_handler) for mui_u8g2_u8_bar_mud_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_get_pi_flags) for mui_u8g2_get_pi_flags
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mud_msg_handler) for mui_u8g2_u8_bar_mud_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_char_wm_mud_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_u8_char_wm_mud_pi) refers to mui_u8g2.o(i.mui_is_valid_char) for mui_is_valid_char
    mui_u8g2.o(i.mui_u8g2_u8_char_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u8_chkbox_wm_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_u8_chkbox_wm_pi) refers to mui_u8g2.o(i.mui_get_y) for mui_get_y
    mui_u8g2.o(i.mui_u8g2_u8_chkbox_wm_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u8_chkbox_wm_pi) refers to mui_u8g2.o(i.u8g2_DrawCheckbox) for u8g2_DrawCheckbox
    mui_u8g2.o(i.mui_u8g2_u8_chkbox_wm_pi) refers to u8g2_font.o(i.u8g2_GetUTF8Width) for u8g2_GetUTF8Width
    mui_u8g2.o(i.mui_u8g2_u8_chkbox_wm_pi) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    mui_u8g2.o(i.mui_u8g2_u8_chkbox_wm_pi) refers to u8g2_button.o(i.u8g2_DrawButtonFrame) for u8g2_DrawButtonFrame
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_get_pf_flags) for mui_u8g2_get_pf_flags
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mse_msg_handler) for mui_u8g2_u8_bar_mse_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_get_pi_flags) for mui_u8g2_get_pi_flags
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mse_msg_handler) for mui_u8g2_u8_bar_mse_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_get_pf_flags) for mui_u8g2_get_pf_flags
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mud_msg_handler) for mui_u8g2_u8_bar_mud_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_get_pi_flags) for mui_u8g2_get_pi_flags
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mud_msg_handler) for mui_u8g2_u8_bar_mud_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_min_max_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pf) for mui_u8g2_u8_vmm_draw_wm_pf
    mui_u8g2.o(i.mui_u8g2_u8_min_max_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pi) for mui_u8g2_u8_vmm_draw_wm_pi
    mui_u8g2.o(i.mui_u8g2_u8_min_max_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pf) for mui_u8g2_u8_vmm_draw_wm_pf
    mui_u8g2.o(i.mui_u8g2_u8_min_max_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pi) for mui_u8g2_u8_vmm_draw_wm_pi
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) refers to mui.o(i.mui_GetSelectableFieldOptionCnt) for mui_GetSelectableFieldOptionCnt
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) refers to mui_u8g2.o(i.mui_u8g2_handle_scroll_next_prev_events) for mui_u8g2_handle_scroll_next_prev_events
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_wm_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_wm_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_wm_pi) refers to mui_u8g2.o(i.mui_get_y) for mui_get_y
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_get_pi_flags) for mui_u8g2_get_pi_flags
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_wm_pi) refers to mui.o(i.mui_GetSelectableFieldTextOption) for mui_GetSelectableFieldTextOption
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_wm_pi) refers to u8g2_button.o(i.u8g2_DrawButtonUTF8) for u8g2_DrawButtonUTF8
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) for mui_u8g2_u8_opt_child_mse_common
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mse_pf) refers to mui.o(i.mui_fds_get_nth_token) for mui_fds_get_nth_token
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pf) for mui_u8g2_draw_button_pf
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mse_pf) refers to mui.o(i.mui_fds_get_token_cnt) for mui_fds_get_token_cnt
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mse_pi) refers to mui.o(i.mui_fds_get_nth_token) for mui_fds_get_nth_token
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mse_pi) refers to mui.o(i.mui_fds_get_token_cnt) for mui_fds_get_token_cnt
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mud_pf) refers to mui.o(i.mui_fds_get_nth_token) for mui_fds_get_nth_token
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pf) for mui_u8g2_draw_button_pf
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mud_pf) refers to mui.o(i.mui_fds_get_token_cnt) for mui_fds_get_token_cnt
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mud_pi) refers to mui.o(i.mui_fds_get_nth_token) for mui_fds_get_nth_token
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mud_pi) refers to mui.o(i.mui_fds_get_token_cnt) for mui_fds_get_token_cnt
    mui_u8g2.o(i.mui_u8g2_u8_opt_parent_wm_pi) refers to mui.o(i.mui_fds_get_nth_token) for mui_fds_get_nth_token
    mui_u8g2.o(i.mui_u8g2_u8_opt_parent_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u8_opt_parent_wm_pi) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_u8_opt_parent_wm_pi) refers to mui.o(i.mui_GotoForm) for mui_GotoForm
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi) refers to mui_u8g2.o(i.mui_get_y) for mui_get_y
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi) refers to mui_u8g2.o(i.u8g2_DrawValueMark) for u8g2_DrawValueMark
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi) refers to mui.o(i.mui_GetSelectableFieldTextOption) for mui_GetSelectableFieldTextOption
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi) refers to u8g2_button.o(i.u8g2_DrawButtonFrame) for u8g2_DrawButtonFrame
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) for mui_u8g2_u8_opt_child_mse_common
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to mui_u8g2.o(i.mui_get_y) for mui_get_y
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to mui_u8g2.o(i.u8g2_DrawValueMark) for u8g2_DrawValueMark
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to mui.o(i.mui_GetSelectableFieldTextOption) for mui_GetSelectableFieldTextOption
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to u8g2_font.o(i.u8g2_GetUTF8Width) for u8g2_GetUTF8Width
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to u8g2_button.o(i.u8g2_DrawButtonFrame) for u8g2_DrawButtonFrame
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) for mui_u8g2_u8_opt_child_mse_common
    mui_u8g2.o(i.mui_u8g2_u8_radio_wm_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_u8_radio_wm_pi) refers to mui_u8g2.o(i.mui_get_y) for mui_get_y
    mui_u8g2.o(i.mui_u8g2_u8_radio_wm_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u8_radio_wm_pi) refers to mui_u8g2.o(i.u8g2_DrawCheckbox) for u8g2_DrawCheckbox
    mui_u8g2.o(i.mui_u8g2_u8_radio_wm_pi) refers to u8g2_font.o(i.u8g2_GetUTF8Width) for u8g2_GetUTF8Width
    mui_u8g2.o(i.mui_u8g2_u8_radio_wm_pi) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    mui_u8g2.o(i.mui_u8g2_u8_radio_wm_pi) refers to u8g2_button.o(i.u8g2_DrawButtonFrame) for u8g2_DrawButtonFrame
    mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pf) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pf) refers to u8x8_u8toa.o(i.u8x8_u8toa) for u8x8_u8toa
    mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pf) refers to u8g2_font.o(i.u8g2_GetStrWidth) for u8g2_GetStrWidth
    mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pf) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pf) for mui_u8g2_draw_button_pf
    mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pi) refers to u8x8_u8toa.o(i.u8x8_u8toa) for u8x8_u8toa
    mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pi) refers to u8g2_font.o(i.u8g2_GetStrWidth) for u8g2_GetStrWidth
    mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_x8_min_max_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pf) for mui_u8g2_x8_vmm_draw_wm_pf
    mui_u8g2.o(i.mui_u8g2_x8_min_max_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pf) for mui_u8g2_x8_vmm_draw_wm_pf
    mui_u8g2.o(i.mui_u8g2_x8_min_max_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pi) for mui_u8g2_x8_vmm_draw_wm_pi
    mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pf) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pf) refers to u8x8_u8toa.o(i.u8x8_u8tox) for u8x8_u8tox
    mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pf) refers to u8g2_font.o(i.u8g2_GetStrWidth) for u8g2_GetStrWidth
    mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pf) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pf) for mui_u8g2_draw_button_pf
    mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pi) refers to mui_u8g2.o(i.mui_get_U8g2) for mui_get_U8g2
    mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pi) refers to u8x8_u8toa.o(i.u8x8_u8tox) for u8x8_u8tox
    mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pi) refers to u8g2_font.o(i.u8g2_GetStrWidth) for u8g2_GetStrWidth
    mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_x8g2_x8_min_max_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pi) for mui_u8g2_x8_vmm_draw_wm_pi
    mui_u8g2.o(i.u8g2_DrawCheckbox) refers to u8g2_box.o(i.u8g2_DrawFrame) for u8g2_DrawFrame
    mui_u8g2.o(i.u8g2_DrawCheckbox) refers to u8g2_box.o(i.u8g2_DrawBox) for u8g2_DrawBox
    mui_u8g2.o(i.u8g2_DrawValueMark) refers to u8g2_box.o(i.u8g2_DrawBox) for u8g2_DrawBox
    u8g2_arc.o(i.u8g2_DrawArc) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_arc.o(i.u8g2_DrawArc) refers to u8g2_arc.o(i.u8g2_draw_arc) for u8g2_draw_arc
    u8g2_arc.o(i.u8g2_draw_arc) refers to u8g2_hvline.o(i.u8g2_DrawPixel) for u8g2_DrawPixel
    u8g2_bitmap.o(i.u8g2_DrawBitmap) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_bitmap.o(i.u8g2_DrawBitmap) refers to u8g2_bitmap.o(i.u8g2_DrawHorizontalBitmap) for u8g2_DrawHorizontalBitmap
    u8g2_bitmap.o(i.u8g2_DrawHXBM) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_bitmap.o(i.u8g2_DrawHXBM) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_bitmap.o(i.u8g2_DrawHXBMP) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_bitmap.o(i.u8g2_DrawHXBMP) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_bitmap.o(i.u8g2_DrawHorizontalBitmap) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_bitmap.o(i.u8g2_DrawHorizontalBitmap) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_bitmap.o(i.u8g2_DrawXBM) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_bitmap.o(i.u8g2_DrawXBM) refers to u8g2_bitmap.o(i.u8g2_DrawHXBM) for u8g2_DrawHXBM
    u8g2_bitmap.o(i.u8g2_DrawXBMP) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_bitmap.o(i.u8g2_DrawXBMP) refers to u8g2_bitmap.o(i.u8g2_DrawHXBMP) for u8g2_DrawHXBMP
    u8g2_box.o(i.u8g2_DrawBox) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_box.o(i.u8g2_DrawBox) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_box.o(i.u8g2_DrawFrame) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_box.o(i.u8g2_DrawFrame) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_box.o(i.u8g2_DrawRBox) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_box.o(i.u8g2_DrawRBox) refers to u8g2_circle.o(i.u8g2_DrawDisc) for u8g2_DrawDisc
    u8g2_box.o(i.u8g2_DrawRBox) refers to u8g2_box.o(i.u8g2_DrawBox) for u8g2_DrawBox
    u8g2_box.o(i.u8g2_DrawRFrame) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_box.o(i.u8g2_DrawRFrame) refers to u8g2_circle.o(i.u8g2_DrawCircle) for u8g2_DrawCircle
    u8g2_box.o(i.u8g2_DrawRFrame) refers to u8g2_hvline.o(i.u8g2_DrawHLine) for u8g2_DrawHLine
    u8g2_box.o(i.u8g2_DrawRFrame) refers to u8g2_hvline.o(i.u8g2_DrawVLine) for u8g2_DrawVLine
    u8g2_buffer.o(i.u8g2_ClearBuffer) refers to memseta.o(.text) for __aeabi_memclr
    u8g2_buffer.o(i.u8g2_FirstPage) refers to u8g2_buffer.o(i.u8g2_ClearBuffer) for u8g2_ClearBuffer
    u8g2_buffer.o(i.u8g2_FirstPage) refers to u8g2_buffer.o(i.u8g2_SetBufferCurrTileRow) for u8g2_SetBufferCurrTileRow
    u8g2_buffer.o(i.u8g2_NextPage) refers to u8g2_buffer.o(i.u8g2_send_buffer) for u8g2_send_buffer
    u8g2_buffer.o(i.u8g2_NextPage) refers to u8x8_display.o(i.u8x8_RefreshDisplay) for u8x8_RefreshDisplay
    u8g2_buffer.o(i.u8g2_NextPage) refers to u8g2_buffer.o(i.u8g2_ClearBuffer) for u8g2_ClearBuffer
    u8g2_buffer.o(i.u8g2_NextPage) refers to u8g2_buffer.o(i.u8g2_SetBufferCurrTileRow) for u8g2_SetBufferCurrTileRow
    u8g2_buffer.o(i.u8g2_SendBuffer) refers to u8g2_buffer.o(i.u8g2_send_buffer) for u8g2_send_buffer
    u8g2_buffer.o(i.u8g2_SendBuffer) refers to u8x8_display.o(i.u8x8_RefreshDisplay) for u8x8_RefreshDisplay
    u8g2_buffer.o(i.u8g2_UpdateDisplay) refers to u8g2_buffer.o(i.u8g2_send_buffer) for u8g2_send_buffer
    u8g2_buffer.o(i.u8g2_UpdateDisplayArea) refers to u8x8_display.o(i.u8x8_DrawTile) for u8x8_DrawTile
    u8g2_buffer.o(i.u8g2_WriteBufferPBM) refers to u8x8_capture.o(i.u8x8_capture_write_pbm_pre) for u8x8_capture_write_pbm_pre
    u8g2_buffer.o(i.u8g2_WriteBufferPBM) refers to u8x8_capture.o(i.u8x8_capture_write_pbm_buffer) for u8x8_capture_write_pbm_buffer
    u8g2_buffer.o(i.u8g2_WriteBufferPBM) refers to u8x8_capture.o(i.u8x8_capture_get_pixel_1) for u8x8_capture_get_pixel_1
    u8g2_buffer.o(i.u8g2_WriteBufferPBM2) refers to u8x8_capture.o(i.u8x8_capture_write_pbm_pre) for u8x8_capture_write_pbm_pre
    u8g2_buffer.o(i.u8g2_WriteBufferPBM2) refers to u8x8_capture.o(i.u8x8_capture_write_pbm_buffer) for u8x8_capture_write_pbm_buffer
    u8g2_buffer.o(i.u8g2_WriteBufferPBM2) refers to u8x8_capture.o(i.u8x8_capture_get_pixel_2) for u8x8_capture_get_pixel_2
    u8g2_buffer.o(i.u8g2_WriteBufferXBM) refers to u8x8_capture.o(i.u8x8_capture_write_xbm_pre) for u8x8_capture_write_xbm_pre
    u8g2_buffer.o(i.u8g2_WriteBufferXBM) refers to u8x8_capture.o(i.u8x8_capture_write_xbm_buffer) for u8x8_capture_write_xbm_buffer
    u8g2_buffer.o(i.u8g2_WriteBufferXBM) refers to u8x8_capture.o(i.u8x8_capture_get_pixel_1) for u8x8_capture_get_pixel_1
    u8g2_buffer.o(i.u8g2_WriteBufferXBM2) refers to u8x8_capture.o(i.u8x8_capture_write_xbm_pre) for u8x8_capture_write_xbm_pre
    u8g2_buffer.o(i.u8g2_WriteBufferXBM2) refers to u8x8_capture.o(i.u8x8_capture_write_xbm_buffer) for u8x8_capture_write_xbm_buffer
    u8g2_buffer.o(i.u8g2_WriteBufferXBM2) refers to u8x8_capture.o(i.u8x8_capture_get_pixel_2) for u8x8_capture_get_pixel_2
    u8g2_buffer.o(i.u8g2_send_buffer) refers to u8g2_buffer.o(i.u8g2_send_tile_row) for u8g2_send_tile_row
    u8g2_buffer.o(i.u8g2_send_tile_row) refers to u8x8_display.o(i.u8x8_DrawTile) for u8x8_DrawTile
    u8g2_button.o(i.u8g2_DrawButtonFrame) refers to u8g2_hvline.o(i.u8g2_SetDrawColor) for u8g2_SetDrawColor
    u8g2_button.o(i.u8g2_DrawButtonFrame) refers to u8g2_box.o(i.u8g2_DrawFrame) for u8g2_DrawFrame
    u8g2_button.o(i.u8g2_DrawButtonFrame) refers to u8g2_hvline.o(i.u8g2_DrawHLine) for u8g2_DrawHLine
    u8g2_button.o(i.u8g2_DrawButtonFrame) refers to u8g2_hvline.o(i.u8g2_DrawVLine) for u8g2_DrawVLine
    u8g2_button.o(i.u8g2_DrawButtonFrame) refers to u8g2_box.o(i.u8g2_DrawBox) for u8g2_DrawBox
    u8g2_button.o(i.u8g2_DrawButtonUTF8) refers to u8g2_font.o(i.u8g2_GetUTF8Width) for u8g2_GetUTF8Width
    u8g2_button.o(i.u8g2_DrawButtonUTF8) refers to u8g2_font.o(i.u8g2_SetFontMode) for u8g2_SetFontMode
    u8g2_button.o(i.u8g2_DrawButtonUTF8) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    u8g2_button.o(i.u8g2_DrawButtonUTF8) refers to u8g2_button.o(i.u8g2_DrawButtonFrame) for u8g2_DrawButtonFrame
    u8g2_circle.o(i.u8g2_DrawCircle) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_circle.o(i.u8g2_DrawCircle) refers to u8g2_circle.o(i.u8g2_draw_circle) for u8g2_draw_circle
    u8g2_circle.o(i.u8g2_DrawDisc) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_circle.o(i.u8g2_DrawDisc) refers to u8g2_circle.o(i.u8g2_draw_disc) for u8g2_draw_disc
    u8g2_circle.o(i.u8g2_DrawEllipse) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_circle.o(i.u8g2_DrawEllipse) refers to u8g2_circle.o(i.u8g2_draw_ellipse) for u8g2_draw_ellipse
    u8g2_circle.o(i.u8g2_DrawFilledEllipse) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_circle.o(i.u8g2_DrawFilledEllipse) refers to u8g2_circle.o(i.u8g2_draw_filled_ellipse) for u8g2_draw_filled_ellipse
    u8g2_circle.o(i.u8g2_draw_circle) refers to u8g2_circle.o(i.u8g2_draw_circle_section) for u8g2_draw_circle_section
    u8g2_circle.o(i.u8g2_draw_circle_section) refers to u8g2_hvline.o(i.u8g2_DrawPixel) for u8g2_DrawPixel
    u8g2_circle.o(i.u8g2_draw_disc) refers to u8g2_circle.o(i.u8g2_draw_disc_section) for u8g2_draw_disc_section
    u8g2_circle.o(i.u8g2_draw_disc_section) refers to u8g2_hvline.o(i.u8g2_DrawVLine) for u8g2_DrawVLine
    u8g2_circle.o(i.u8g2_draw_ellipse) refers to u8g2_circle.o(i.u8g2_draw_ellipse_section) for u8g2_draw_ellipse_section
    u8g2_circle.o(i.u8g2_draw_ellipse_section) refers to u8g2_hvline.o(i.u8g2_DrawPixel) for u8g2_DrawPixel
    u8g2_circle.o(i.u8g2_draw_filled_ellipse) refers to u8g2_circle.o(i.u8g2_draw_filled_ellipse_section) for u8g2_draw_filled_ellipse_section
    u8g2_circle.o(i.u8g2_draw_filled_ellipse_section) refers to u8g2_hvline.o(i.u8g2_DrawVLine) for u8g2_DrawVLine
    u8g2_cleardisplay.o(i.u8g2_ClearDisplay) refers to u8g2_buffer.o(i.u8g2_FirstPage) for u8g2_FirstPage
    u8g2_cleardisplay.o(i.u8g2_ClearDisplay) refers to u8g2_buffer.o(i.u8g2_NextPage) for u8g2_NextPage
    u8g2_cleardisplay.o(i.u8g2_ClearDisplay) refers to u8g2_buffer.o(i.u8g2_SetBufferCurrTileRow) for u8g2_SetBufferCurrTileRow
    u8g2_d_memory.o(i.u8g2_m_16_8_f) refers to u8g2_d_memory.o(.bss) for buf
    u8g2_d_setup.o(i.u8g2_Setup_ssd1315_i2c_128x64_noname_f) refers to u8x8_setup.o(i.u8x8_Setup) for u8x8_Setup
    u8g2_d_setup.o(i.u8g2_Setup_ssd1315_i2c_128x64_noname_f) refers to u8g2_d_memory.o(i.u8g2_m_16_8_f) for u8g2_m_16_8_f
    u8g2_d_setup.o(i.u8g2_Setup_ssd1315_i2c_128x64_noname_f) refers to u8g2_setup.o(i.u8g2_SetupBuffer) for u8g2_SetupBuffer
    u8g2_d_setup.o(i.u8g2_Setup_ssd1315_i2c_128x64_noname_f) refers to u8x8_cad.o(i.u8x8_cad_ssd13xx_fast_i2c) for u8x8_cad_ssd13xx_fast_i2c
    u8g2_d_setup.o(i.u8g2_Setup_ssd1315_i2c_128x64_noname_f) refers to u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_128x64_noname) for u8x8_d_ssd1315_128x64_noname
    u8g2_d_setup.o(i.u8g2_Setup_ssd1315_i2c_128x64_noname_f) refers to u8g2_ll_hvline.o(i.u8g2_ll_hvline_vertical_top_lsb) for u8g2_ll_hvline_vertical_top_lsb
    u8g2_font.o(i.u8g2_DrawExtUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8g2_font.o(i.u8g2_DrawExtUTF8) refers to u8g2_font.o(i.u8g2_GetGlyphWidth) for u8g2_GetGlyphWidth
    u8g2_font.o(i.u8g2_DrawExtUTF8) refers to u8g2_kerning.o(i.u8g2_GetKerningByTable) for u8g2_GetKerningByTable
    u8g2_font.o(i.u8g2_DrawExtUTF8) refers to u8g2_font.o(i.u8g2_DrawGlyph) for u8g2_DrawGlyph
    u8g2_font.o(i.u8g2_DrawExtUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8g2_font.o(i.u8g2_DrawExtendedUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8g2_font.o(i.u8g2_DrawExtendedUTF8) refers to u8g2_font.o(i.u8g2_GetGlyphWidth) for u8g2_GetGlyphWidth
    u8g2_font.o(i.u8g2_DrawExtendedUTF8) refers to u8g2_kerning.o(i.u8g2_GetKerning) for u8g2_GetKerning
    u8g2_font.o(i.u8g2_DrawExtendedUTF8) refers to u8g2_font.o(i.u8g2_DrawGlyph) for u8g2_DrawGlyph
    u8g2_font.o(i.u8g2_DrawExtendedUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8g2_font.o(i.u8g2_DrawGlyph) refers to u8g2_font.o(i.u8g2_font_draw_glyph) for u8g2_font_draw_glyph
    u8g2_font.o(i.u8g2_DrawGlyphX2) refers to u8g2_font.o(i.u8g2_font_2x_draw_glyph) for u8g2_font_2x_draw_glyph
    u8g2_font.o(i.u8g2_DrawHB) refers to u8g2_font.o(i.u8g2_DrawGlyph) for u8g2_DrawGlyph
    u8g2_font.o(i.u8g2_DrawStr) refers to u8g2_font.o(i.u8g2_draw_string) for u8g2_draw_string
    u8g2_font.o(i.u8g2_DrawStr) refers to u8x8_8x8.o(i.u8x8_ascii_next) for u8x8_ascii_next
    u8g2_font.o(i.u8g2_DrawStrX2) refers to u8g2_font.o(i.u8g2_draw_string_2x) for u8g2_draw_string_2x
    u8g2_font.o(i.u8g2_DrawStrX2) refers to u8x8_8x8.o(i.u8x8_ascii_next) for u8x8_ascii_next
    u8g2_font.o(i.u8g2_DrawUTF8) refers to u8g2_font.o(i.u8g2_draw_string) for u8g2_draw_string
    u8g2_font.o(i.u8g2_DrawUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8g2_font.o(i.u8g2_DrawUTF8X2) refers to u8g2_font.o(i.u8g2_draw_string_2x) for u8g2_draw_string_2x
    u8g2_font.o(i.u8g2_DrawUTF8X2) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8g2_font.o(i.u8g2_GetFontSize) refers to u8g2_font.o(i.u8g2_font_get_word) for u8g2_font_get_word
    u8g2_font.o(i.u8g2_GetGlyphHorizontalProperties) refers to u8g2_font.o(i.u8g2_font_get_glyph_data) for u8g2_font_get_glyph_data
    u8g2_font.o(i.u8g2_GetGlyphHorizontalProperties) refers to u8g2_font.o(i.u8g2_font_setup_decode) for u8g2_font_setup_decode
    u8g2_font.o(i.u8g2_GetGlyphHorizontalProperties) refers to u8g2_font.o(i.u8g2_font_decode_get_signed_bits) for u8g2_font_decode_get_signed_bits
    u8g2_font.o(i.u8g2_GetGlyphWidth) refers to u8g2_font.o(i.u8g2_font_get_glyph_data) for u8g2_font_get_glyph_data
    u8g2_font.o(i.u8g2_GetGlyphWidth) refers to u8g2_font.o(i.u8g2_font_setup_decode) for u8g2_font_setup_decode
    u8g2_font.o(i.u8g2_GetGlyphWidth) refers to u8g2_font.o(i.u8g2_font_decode_get_signed_bits) for u8g2_font_decode_get_signed_bits
    u8g2_font.o(i.u8g2_GetStrWidth) refers to u8g2_font.o(i.u8g2_string_width) for u8g2_string_width
    u8g2_font.o(i.u8g2_GetStrWidth) refers to u8x8_8x8.o(i.u8x8_ascii_next) for u8x8_ascii_next
    u8g2_font.o(i.u8g2_GetStrX) refers to u8g2_font.o(i.u8g2_GetGlyphHorizontalProperties) for u8g2_GetGlyphHorizontalProperties
    u8g2_font.o(i.u8g2_GetUTF8Width) refers to u8g2_font.o(i.u8g2_string_width) for u8g2_string_width
    u8g2_font.o(i.u8g2_GetUTF8Width) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8g2_font.o(i.u8g2_GetXOffsetGlyph) refers to u8g2_font.o(i.u8g2_GetGlyphWidth) for u8g2_GetGlyphWidth
    u8g2_font.o(i.u8g2_GetXOffsetUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8g2_font.o(i.u8g2_GetXOffsetUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8g2_font.o(i.u8g2_GetXOffsetUTF8) refers to u8g2_font.o(i.u8g2_GetXOffsetGlyph) for u8g2_GetXOffsetGlyph
    u8g2_font.o(i.u8g2_IsAllValidUTF8) refers to u8g2_font.o(i.u8g2_is_all_valid) for u8g2_is_all_valid
    u8g2_font.o(i.u8g2_IsAllValidUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8g2_font.o(i.u8g2_IsGlyph) refers to u8g2_font.o(i.u8g2_font_get_glyph_data) for u8g2_font_get_glyph_data
    u8g2_font.o(i.u8g2_SetFont) refers to u8g2_font.o(i.u8g2_read_font_info) for u8g2_read_font_info
    u8g2_font.o(i.u8g2_SetFont) refers to u8g2_font.o(i.u8g2_UpdateRefHeight) for u8g2_UpdateRefHeight
    u8g2_font.o(i.u8g2_SetFontPosBaseline) refers to u8g2_font.o(i.u8g2_font_calc_vref_font) for u8g2_font_calc_vref_font
    u8g2_font.o(i.u8g2_SetFontPosBottom) refers to u8g2_font.o(i.u8g2_font_calc_vref_bottom) for u8g2_font_calc_vref_bottom
    u8g2_font.o(i.u8g2_SetFontPosCenter) refers to u8g2_font.o(i.u8g2_font_calc_vref_center) for u8g2_font_calc_vref_center
    u8g2_font.o(i.u8g2_SetFontPosTop) refers to u8g2_font.o(i.u8g2_font_calc_vref_top) for u8g2_font_calc_vref_top
    u8g2_font.o(i.u8g2_SetFontRefHeightAll) refers to u8g2_font.o(i.u8g2_UpdateRefHeight) for u8g2_UpdateRefHeight
    u8g2_font.o(i.u8g2_SetFontRefHeightExtendedText) refers to u8g2_font.o(i.u8g2_UpdateRefHeight) for u8g2_UpdateRefHeight
    u8g2_font.o(i.u8g2_SetFontRefHeightText) refers to u8g2_font.o(i.u8g2_UpdateRefHeight) for u8g2_UpdateRefHeight
    u8g2_font.o(i.u8g2_draw_string) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8g2_font.o(i.u8g2_draw_string) refers to u8g2_font.o(i.u8g2_DrawGlyph) for u8g2_DrawGlyph
    u8g2_font.o(i.u8g2_draw_string_2x) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8g2_font.o(i.u8g2_draw_string_2x) refers to u8g2_font.o(i.u8g2_DrawGlyphX2) for u8g2_DrawGlyphX2
    u8g2_font.o(i.u8g2_font_2x_decode_glyph) refers to u8g2_font.o(i.u8g2_font_setup_decode) for u8g2_font_setup_decode
    u8g2_font.o(i.u8g2_font_2x_decode_glyph) refers to u8g2_font.o(i.u8g2_font_decode_get_signed_bits) for u8g2_font_decode_get_signed_bits
    u8g2_font.o(i.u8g2_font_2x_decode_glyph) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_font.o(i.u8g2_font_2x_decode_glyph) refers to u8g2_font.o(i.u8g2_font_decode_get_unsigned_bits) for u8g2_font_decode_get_unsigned_bits
    u8g2_font.o(i.u8g2_font_2x_decode_glyph) refers to u8g2_font.o(i.u8g2_font_2x_decode_len) for u8g2_font_2x_decode_len
    u8g2_font.o(i.u8g2_font_2x_decode_len) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_font.o(i.u8g2_font_2x_draw_glyph) refers to u8g2_font.o(i.u8g2_font_get_glyph_data) for u8g2_font_get_glyph_data
    u8g2_font.o(i.u8g2_font_2x_draw_glyph) refers to u8g2_font.o(i.u8g2_font_2x_decode_glyph) for u8g2_font_2x_decode_glyph
    u8g2_font.o(i.u8g2_font_decode_get_signed_bits) refers to u8g2_font.o(i.u8g2_font_decode_get_unsigned_bits) for u8g2_font_decode_get_unsigned_bits
    u8g2_font.o(i.u8g2_font_decode_glyph) refers to u8g2_font.o(i.u8g2_font_setup_decode) for u8g2_font_setup_decode
    u8g2_font.o(i.u8g2_font_decode_glyph) refers to u8g2_font.o(i.u8g2_font_decode_get_signed_bits) for u8g2_font_decode_get_signed_bits
    u8g2_font.o(i.u8g2_font_decode_glyph) refers to u8g2_font.o(i.u8g2_add_vector_x) for u8g2_add_vector_x
    u8g2_font.o(i.u8g2_font_decode_glyph) refers to u8g2_font.o(i.u8g2_add_vector_y) for u8g2_add_vector_y
    u8g2_font.o(i.u8g2_font_decode_glyph) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_font.o(i.u8g2_font_decode_glyph) refers to u8g2_font.o(i.u8g2_font_decode_get_unsigned_bits) for u8g2_font_decode_get_unsigned_bits
    u8g2_font.o(i.u8g2_font_decode_glyph) refers to u8g2_font.o(i.u8g2_font_decode_len) for u8g2_font_decode_len
    u8g2_font.o(i.u8g2_font_decode_len) refers to u8g2_font.o(i.u8g2_add_vector_x) for u8g2_add_vector_x
    u8g2_font.o(i.u8g2_font_decode_len) refers to u8g2_font.o(i.u8g2_add_vector_y) for u8g2_add_vector_y
    u8g2_font.o(i.u8g2_font_decode_len) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_font.o(i.u8g2_font_draw_glyph) refers to u8g2_font.o(i.u8g2_font_get_glyph_data) for u8g2_font_get_glyph_data
    u8g2_font.o(i.u8g2_font_draw_glyph) refers to u8g2_font.o(i.u8g2_font_decode_glyph) for u8g2_font_decode_glyph
    u8g2_font.o(i.u8g2_font_get_glyph_data) refers to u8g2_font.o(i.u8g2_font_get_word) for u8g2_font_get_word
    u8g2_font.o(i.u8g2_font_setup_decode) refers to u8g2_font.o(i.u8g2_font_decode_get_unsigned_bits) for u8g2_font_decode_get_unsigned_bits
    u8g2_font.o(i.u8g2_is_all_valid) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8g2_font.o(i.u8g2_is_all_valid) refers to u8g2_font.o(i.u8g2_font_get_glyph_data) for u8g2_font_get_glyph_data
    u8g2_font.o(i.u8g2_read_font_info) refers to u8g2_font.o(i.u8g2_font_get_byte) for u8g2_font_get_byte
    u8g2_font.o(i.u8g2_read_font_info) refers to u8g2_font.o(i.u8g2_font_get_word) for u8g2_font_get_word
    u8g2_font.o(i.u8g2_string_width) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8g2_font.o(i.u8g2_string_width) refers to u8g2_font.o(i.u8g2_GetGlyphWidth) for u8g2_GetGlyphWidth
    u8g2_hvline.o(i.u8g2_DrawHLine) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_hvline.o(i.u8g2_DrawHVLine) refers to u8g2_hvline.o(i.u8g2_clip_intersection2) for u8g2_clip_intersection2
    u8g2_hvline.o(i.u8g2_DrawPixel) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_hvline.o(i.u8g2_DrawVLine) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8g2_font.o(i.u8g2_SetFontDirection) for u8g2_SetFontDirection
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8g2_font.o(i.u8g2_SetFontPosBaseline) for u8g2_SetFontPosBaseline
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8g2_font.o(i.u8g2_GetUTF8Width) for u8g2_GetUTF8Width
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8g2_buffer.o(i.u8g2_FirstPage) for u8g2_FirstPage
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8g2_selection_list.o(i.u8g2_DrawUTF8Lines) for u8g2_DrawUTF8Lines
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8x8_u8toa.o(i.u8x8_u8toa) for u8x8_u8toa
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8g2_buffer.o(i.u8g2_NextPage) for u8g2_NextPage
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8x8_debounce.o(i.u8x8_GetMenuEvent) for u8x8_GetMenuEvent
    u8g2_intersection.o(i.u8g2_IsIntersection) refers to u8g2_intersection.o(i.u8g2_is_intersection_decision_tree) for u8g2_is_intersection_decision_tree
    u8g2_line.o(i.u8g2_DrawLine) refers to u8g2_hvline.o(i.u8g2_DrawPixel) for u8g2_DrawPixel
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8g2_font.o(i.u8g2_SetFontDirection) for u8g2_SetFontDirection
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8g2_font.o(i.u8g2_SetFontPosBaseline) for u8g2_SetFontPosBaseline
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8g2_buffer.o(i.u8g2_FirstPage) for u8g2_FirstPage
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8g2_selection_list.o(i.u8g2_DrawUTF8Lines) for u8g2_DrawUTF8Lines
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8g2_selection_list.o(i.u8g2_DrawUTF8Line) for u8g2_DrawUTF8Line
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8g2_message.o(i.u8g2_draw_button_line) for u8g2_draw_button_line
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8g2_buffer.o(i.u8g2_NextPage) for u8g2_NextPage
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8x8_debounce.o(i.u8x8_GetMenuEvent) for u8x8_GetMenuEvent
    u8g2_message.o(i.u8g2_draw_button_line) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8g2_message.o(i.u8g2_draw_button_line) refers to u8x8_string.o(i.u8x8_GetStringLineStart) for u8x8_GetStringLineStart
    u8g2_message.o(i.u8g2_draw_button_line) refers to u8g2_font.o(i.u8g2_GetUTF8Width) for u8g2_GetUTF8Width
    u8g2_message.o(i.u8g2_draw_button_line) refers to u8g2_selection_list.o(i.u8g2_DrawUTF8Line) for u8g2_DrawUTF8Line
    u8g2_polygon.o(i.pg_DrawPolygon) refers to u8g2_polygon.o(i.pg_prepare) for pg_prepare
    u8g2_polygon.o(i.pg_DrawPolygon) refers to u8g2_polygon.o(i.pg_exec) for pg_exec
    u8g2_polygon.o(i.pg_exec) refers to u8g2_polygon.o(i.pg_line_init) for pg_line_init
    u8g2_polygon.o(i.pg_exec) refers to u8g2_polygon.o(i.pge_Next) for pge_Next
    u8g2_polygon.o(i.pg_exec) refers to u8g2_polygon.o(i.pg_hline) for pg_hline
    u8g2_polygon.o(i.pg_hline) refers to u8g2_hvline.o(i.u8g2_DrawHLine) for u8g2_DrawHLine
    u8g2_polygon.o(i.pg_line_init) refers to u8g2_polygon.o(i.pge_Init) for pge_Init
    u8g2_polygon.o(i.pg_prepare) refers to u8g2_polygon.o(i.pg_expand_min_y) for pg_expand_min_y
    u8g2_polygon.o(i.pg_prepare) refers to u8g2_polygon.o(i.pg_inc) for pg_inc
    u8g2_polygon.o(i.pg_prepare) refers to u8g2_polygon.o(i.pg_dec) for pg_dec
    u8g2_polygon.o(i.u8g2_AddPolygonXY) refers to u8g2_polygon.o(i.pg_AddPolygonXY) for pg_AddPolygonXY
    u8g2_polygon.o(i.u8g2_AddPolygonXY) refers to u8g2_polygon.o(.bss) for u8g2_pg
    u8g2_polygon.o(i.u8g2_ClearPolygonXY) refers to u8g2_polygon.o(i.pg_ClearPolygonXY) for pg_ClearPolygonXY
    u8g2_polygon.o(i.u8g2_ClearPolygonXY) refers to u8g2_polygon.o(.bss) for u8g2_pg
    u8g2_polygon.o(i.u8g2_DrawPolygon) refers to u8g2_polygon.o(i.pg_DrawPolygon) for pg_DrawPolygon
    u8g2_polygon.o(i.u8g2_DrawPolygon) refers to u8g2_polygon.o(.bss) for u8g2_pg
    u8g2_polygon.o(i.u8g2_DrawTriangle) refers to u8g2_polygon.o(i.u8g2_ClearPolygonXY) for u8g2_ClearPolygonXY
    u8g2_polygon.o(i.u8g2_DrawTriangle) refers to u8g2_polygon.o(i.u8g2_AddPolygonXY) for u8g2_AddPolygonXY
    u8g2_polygon.o(i.u8g2_DrawTriangle) refers to u8g2_polygon.o(i.u8g2_DrawPolygon) for u8g2_DrawPolygon
    u8g2_selection_list.o(i.u8g2_DrawSelectionList) refers to u8g2_selection_list.o(i.u8g2_draw_selection_list_line) for u8g2_draw_selection_list_line
    u8g2_selection_list.o(i.u8g2_DrawUTF8Line) refers to u8g2_font.o(i.u8g2_SetFontDirection) for u8g2_SetFontDirection
    u8g2_selection_list.o(i.u8g2_DrawUTF8Line) refers to u8g2_font.o(i.u8g2_GetUTF8Width) for u8g2_GetUTF8Width
    u8g2_selection_list.o(i.u8g2_DrawUTF8Line) refers to u8g2_font.o(i.u8g2_GetXOffsetUTF8) for u8g2_GetXOffsetUTF8
    u8g2_selection_list.o(i.u8g2_DrawUTF8Line) refers to u8g2_hvline.o(i.u8g2_SetDrawColor) for u8g2_SetDrawColor
    u8g2_selection_list.o(i.u8g2_DrawUTF8Line) refers to u8g2_box.o(i.u8g2_DrawBox) for u8g2_DrawBox
    u8g2_selection_list.o(i.u8g2_DrawUTF8Line) refers to u8g2_box.o(i.u8g2_DrawFrame) for u8g2_DrawFrame
    u8g2_selection_list.o(i.u8g2_DrawUTF8Line) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    u8g2_selection_list.o(i.u8g2_DrawUTF8Lines) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8g2_selection_list.o(i.u8g2_DrawUTF8Lines) refers to u8x8_string.o(i.u8x8_GetStringLineStart) for u8x8_GetStringLineStart
    u8g2_selection_list.o(i.u8g2_DrawUTF8Lines) refers to u8g2_selection_list.o(i.u8g2_DrawUTF8Line) for u8g2_DrawUTF8Line
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8g2_font.o(i.u8g2_SetFontPosBaseline) for u8g2_SetFontPosBaseline
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8g2_buffer.o(i.u8g2_FirstPage) for u8g2_FirstPage
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8g2_selection_list.o(i.u8g2_DrawUTF8Lines) for u8g2_DrawUTF8Lines
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8g2_hvline.o(i.u8g2_DrawHLine) for u8g2_DrawHLine
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8g2_selection_list.o(i.u8g2_DrawSelectionList) for u8g2_DrawSelectionList
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8g2_buffer.o(i.u8g2_NextPage) for u8g2_NextPage
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8x8_debounce.o(i.u8x8_GetMenuEvent) for u8x8_GetMenuEvent
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8x8_selection_list.o(i.u8sl_Next) for u8sl_Next
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8x8_selection_list.o(i.u8sl_Prev) for u8sl_Prev
    u8g2_selection_list.o(i.u8g2_draw_selection_list_line) refers to u8x8_string.o(i.u8x8_GetStringLineStart) for u8x8_GetStringLineStart
    u8g2_selection_list.o(i.u8g2_draw_selection_list_line) refers to u8g2_selection_list.o(i.u8g2_DrawUTF8Line) for u8g2_DrawUTF8Line
    u8g2_setup.o(i.u8g2_SendF) refers to u8x8_cad.o(i.u8x8_cad_vsendf) for u8x8_cad_vsendf
    u8g2_setup.o(i.u8g2_SetupBuffer) refers to u8g2_setup.o(i.u8g2_SetMaxClipWindow) for u8g2_SetMaxClipWindow
    u8g2_setup.o(i.u8g2_SetupBuffer) refers to u8g2_font.o(i.u8g2_SetFontPosBaseline) for u8g2_SetFontPosBaseline
    u8g2_setup.o(i.u8g2_Setup_null) refers to u8x8_setup.o(i.u8x8_Setup) for u8x8_Setup
    u8g2_setup.o(i.u8g2_Setup_null) refers to u8g2_setup.o(i.u8g2_SetupBuffer) for u8g2_SetupBuffer
    u8g2_setup.o(i.u8g2_Setup_null) refers to u8x8_cad.o(i.u8x8_cad_empty) for u8x8_cad_empty
    u8g2_setup.o(i.u8g2_Setup_null) refers to u8x8_setup.o(i.u8x8_d_null_cb) for u8x8_d_null_cb
    u8g2_setup.o(i.u8g2_Setup_null) refers to u8g2_ll_hvline.o(i.u8g2_ll_hvline_vertical_top_lsb) for u8g2_ll_hvline_vertical_top_lsb
    u8g2_setup.o(i.u8g2_Setup_null) refers to u8g2_setup.o(.data) for buf
    u8g2_setup.o(i.u8g2_apply_clip_window) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_setup.o(i.u8g2_draw_l90_mirrorr_r0) refers to u8g2_hvline.o(i.u8g2_draw_hv_line_2dir) for u8g2_draw_hv_line_2dir
    u8g2_setup.o(i.u8g2_draw_l90_r0) refers to u8g2_hvline.o(i.u8g2_draw_hv_line_2dir) for u8g2_draw_hv_line_2dir
    u8g2_setup.o(i.u8g2_draw_l90_r1) refers to u8g2_hvline.o(i.u8g2_draw_hv_line_2dir) for u8g2_draw_hv_line_2dir
    u8g2_setup.o(i.u8g2_draw_l90_r2) refers to u8g2_hvline.o(i.u8g2_draw_hv_line_2dir) for u8g2_draw_hv_line_2dir
    u8g2_setup.o(i.u8g2_draw_l90_r3) refers to u8g2_hvline.o(i.u8g2_draw_hv_line_2dir) for u8g2_draw_hv_line_2dir
    u8g2_setup.o(i.u8g2_draw_mirror_vertical_r0) refers to u8g2_hvline.o(i.u8g2_draw_hv_line_2dir) for u8g2_draw_hv_line_2dir
    u8g2_setup.o(i.u8g2_update_dimension_r0) refers to u8g2_setup.o(i.u8g2_update_dimension_common) for u8g2_update_dimension_common
    u8g2_setup.o(i.u8g2_update_dimension_r1) refers to u8g2_setup.o(i.u8g2_update_dimension_common) for u8g2_update_dimension_common
    u8g2_setup.o(i.u8g2_update_dimension_r2) refers to u8g2_setup.o(i.u8g2_update_dimension_common) for u8g2_update_dimension_common
    u8g2_setup.o(i.u8g2_update_dimension_r3) refers to u8g2_setup.o(i.u8g2_update_dimension_common) for u8g2_update_dimension_common
    u8g2_setup.o(i.u8g2_update_page_win_r0) refers to u8g2_setup.o(i.u8g2_apply_clip_window) for u8g2_apply_clip_window
    u8g2_setup.o(i.u8g2_update_page_win_r1) refers to u8g2_setup.o(i.u8g2_apply_clip_window) for u8g2_apply_clip_window
    u8g2_setup.o(i.u8g2_update_page_win_r2) refers to u8g2_setup.o(i.u8g2_apply_clip_window) for u8g2_apply_clip_window
    u8g2_setup.o(i.u8g2_update_page_win_r3) refers to u8g2_setup.o(i.u8g2_apply_clip_window) for u8g2_apply_clip_window
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_dimension_r0) for u8g2_update_dimension_r0
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_page_win_r0) for u8g2_update_page_win_r0
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_draw_l90_r0) for u8g2_draw_l90_r0
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_dimension_r1) for u8g2_update_dimension_r1
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_page_win_r1) for u8g2_update_page_win_r1
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_draw_l90_r1) for u8g2_draw_l90_r1
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_dimension_r2) for u8g2_update_dimension_r2
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_page_win_r2) for u8g2_update_page_win_r2
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_draw_l90_r2) for u8g2_draw_l90_r2
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_dimension_r3) for u8g2_update_dimension_r3
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_page_win_r3) for u8g2_update_page_win_r3
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_draw_l90_r3) for u8g2_draw_l90_r3
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_draw_l90_mirrorr_r0) for u8g2_draw_l90_mirrorr_r0
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_draw_mirror_vertical_r0) for u8g2_draw_mirror_vertical_r0
    u8log.o(i.u8log_Init) refers to memseta.o(.text) for __aeabi_memclr4
    u8log.o(i.u8log_Init) refers to u8log.o(i.u8log_clear_screen) for u8log_clear_screen
    u8log.o(i.u8log_WriteChar) refers to u8log.o(i.u8log_write_char) for u8log_write_char
    u8log.o(i.u8log_WriteDec16) refers to u8x8_u16toa.o(i.u8x8_u16toa) for u8x8_u16toa
    u8log.o(i.u8log_WriteDec16) refers to u8log.o(i.u8log_WriteString) for u8log_WriteString
    u8log.o(i.u8log_WriteDec8) refers to u8x8_u8toa.o(i.u8x8_u8toa) for u8x8_u8toa
    u8log.o(i.u8log_WriteDec8) refers to u8log.o(i.u8log_WriteString) for u8log_WriteString
    u8log.o(i.u8log_WriteHex16) refers to u8log.o(i.u8log_WriteHex8) for u8log_WriteHex8
    u8log.o(i.u8log_WriteHex32) refers to u8log.o(i.u8log_WriteHex16) for u8log_WriteHex16
    u8log.o(i.u8log_WriteHex8) refers to u8log.o(i.u8log_WriteHexHalfByte) for u8log_WriteHexHalfByte
    u8log.o(i.u8log_WriteHexHalfByte) refers to u8log.o(i.u8log_WriteChar) for u8log_WriteChar
    u8log.o(i.u8log_WriteString) refers to u8log.o(i.u8log_WriteChar) for u8log_WriteChar
    u8log.o(i.u8log_cursor_on_screen) refers to u8log.o(i.u8log_scroll_up) for u8log_scroll_up
    u8log.o(i.u8log_write_char) refers to u8log.o(i.u8log_cursor_on_screen) for u8log_cursor_on_screen
    u8log.o(i.u8log_write_char) refers to u8log.o(i.u8log_clear_screen) for u8log_clear_screen
    u8log.o(i.u8log_write_char) refers to u8log.o(i.u8log_write_to_screen) for u8log_write_to_screen
    u8log.o(i.u8log_write_to_screen) refers to u8log.o(i.u8log_cursor_on_screen) for u8log_cursor_on_screen
    u8log_u8g2.o(i.u8g2_DrawLog) refers to u8g2_font.o(i.u8g2_SetFontDirection) for u8g2_SetFontDirection
    u8log_u8g2.o(i.u8g2_DrawLog) refers to u8g2_font.o(i.u8g2_DrawGlyph) for u8g2_DrawGlyph
    u8log_u8g2.o(i.u8log_u8g2_cb) refers to u8g2_buffer.o(i.u8g2_FirstPage) for u8g2_FirstPage
    u8log_u8g2.o(i.u8log_u8g2_cb) refers to u8log_u8g2.o(i.u8g2_DrawLog) for u8g2_DrawLog
    u8log_u8g2.o(i.u8log_u8g2_cb) refers to u8g2_buffer.o(i.u8g2_NextPage) for u8g2_NextPage
    u8log_u8x8.o(i.u8log_u8x8_cb) refers to u8log_u8x8.o(i.u8x8_DrawLog) for u8x8_DrawLog
    u8log_u8x8.o(i.u8log_u8x8_cb) refers to u8log_u8x8.o(i.u8x8_DrawLogLine) for u8x8_DrawLogLine
    u8log_u8x8.o(i.u8x8_DrawLog) refers to u8log_u8x8.o(i.u8x8_DrawLogLine) for u8x8_DrawLogLine
    u8log_u8x8.o(i.u8x8_DrawLogLine) refers to u8x8_8x8.o(i.u8x8_DrawGlyph) for u8x8_DrawGlyph
    u8x8_8x8.o(i.u8x8_Draw1x2Glyph) refers to u8x8_8x8.o(i.u8x8_draw_1x2_subglyph) for u8x8_draw_1x2_subglyph
    u8x8_8x8.o(i.u8x8_Draw1x2String) refers to u8x8_8x8.o(i.u8x8_draw_1x2_string) for u8x8_draw_1x2_string
    u8x8_8x8.o(i.u8x8_Draw1x2String) refers to u8x8_8x8.o(i.u8x8_ascii_next) for u8x8_ascii_next
    u8x8_8x8.o(i.u8x8_Draw1x2UTF8) refers to u8x8_8x8.o(i.u8x8_draw_1x2_string) for u8x8_draw_1x2_string
    u8x8_8x8.o(i.u8x8_Draw1x2UTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8x8_8x8.o(i.u8x8_Draw2x2Glyph) refers to u8x8_8x8.o(i.u8x8_draw_2x2_subglyph) for u8x8_draw_2x2_subglyph
    u8x8_8x8.o(i.u8x8_Draw2x2String) refers to u8x8_8x8.o(i.u8x8_draw_2x2_string) for u8x8_draw_2x2_string
    u8x8_8x8.o(i.u8x8_Draw2x2String) refers to u8x8_8x8.o(i.u8x8_ascii_next) for u8x8_ascii_next
    u8x8_8x8.o(i.u8x8_Draw2x2UTF8) refers to u8x8_8x8.o(i.u8x8_draw_2x2_string) for u8x8_draw_2x2_string
    u8x8_8x8.o(i.u8x8_Draw2x2UTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8x8_8x8.o(i.u8x8_DrawGlyph) refers to u8x8_8x8.o(i.u8x8_get_glyph_data) for u8x8_get_glyph_data
    u8x8_8x8.o(i.u8x8_DrawGlyph) refers to u8x8_display.o(i.u8x8_DrawTile) for u8x8_DrawTile
    u8x8_8x8.o(i.u8x8_DrawString) refers to u8x8_8x8.o(i.u8x8_draw_string) for u8x8_draw_string
    u8x8_8x8.o(i.u8x8_DrawString) refers to u8x8_8x8.o(i.u8x8_ascii_next) for u8x8_ascii_next
    u8x8_8x8.o(i.u8x8_DrawUTF8) refers to u8x8_8x8.o(i.u8x8_draw_string) for u8x8_draw_string
    u8x8_8x8.o(i.u8x8_DrawUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8x8_8x8.o(i.u8x8_GetUTF8Len) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8x8_8x8.o(i.u8x8_GetUTF8Len) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8x8_8x8.o(i.u8x8_draw_1x2_string) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8x8_8x8.o(i.u8x8_draw_1x2_string) refers to u8x8_8x8.o(i.u8x8_Draw1x2Glyph) for u8x8_Draw1x2Glyph
    u8x8_8x8.o(i.u8x8_draw_1x2_subglyph) refers to u8x8_8x8.o(i.u8x8_get_glyph_data) for u8x8_get_glyph_data
    u8x8_8x8.o(i.u8x8_draw_1x2_subglyph) refers to u8x8_8x8.o(i.u8x8_upscale_byte) for u8x8_upscale_byte
    u8x8_8x8.o(i.u8x8_draw_1x2_subglyph) refers to u8x8_display.o(i.u8x8_DrawTile) for u8x8_DrawTile
    u8x8_8x8.o(i.u8x8_draw_2x2_string) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8x8_8x8.o(i.u8x8_draw_2x2_string) refers to u8x8_8x8.o(i.u8x8_Draw2x2Glyph) for u8x8_Draw2x2Glyph
    u8x8_8x8.o(i.u8x8_draw_2x2_subglyph) refers to u8x8_8x8.o(i.u8x8_get_glyph_data) for u8x8_get_glyph_data
    u8x8_8x8.o(i.u8x8_draw_2x2_subglyph) refers to u8x8_8x8.o(i.u8x8_upscale_byte) for u8x8_upscale_byte
    u8x8_8x8.o(i.u8x8_draw_2x2_subglyph) refers to u8x8_8x8.o(i.u8x8_upscale_buf) for u8x8_upscale_buf
    u8x8_8x8.o(i.u8x8_draw_2x2_subglyph) refers to u8x8_display.o(i.u8x8_DrawTile) for u8x8_DrawTile
    u8x8_8x8.o(i.u8x8_draw_string) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8x8_8x8.o(i.u8x8_draw_string) refers to u8x8_8x8.o(i.u8x8_DrawGlyph) for u8x8_DrawGlyph
    u8x8_byte.o(i.i2c_clear_scl) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.i2c_clear_sda) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.i2c_delay) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.i2c_init) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.i2c_init) refers to u8x8_byte.o(i.i2c_delay) for i2c_delay
    u8x8_byte.o(i.i2c_read_bit) refers to u8x8_byte.o(i.i2c_read_sda) for i2c_read_sda
    u8x8_byte.o(i.i2c_read_bit) refers to u8x8_byte.o(i.i2c_delay) for i2c_delay
    u8x8_byte.o(i.i2c_read_bit) refers to u8x8_byte.o(i.i2c_read_scl_and_delay) for i2c_read_scl_and_delay
    u8x8_byte.o(i.i2c_read_bit) refers to u8x8_byte.o(i.i2c_clear_scl) for i2c_clear_scl
    u8x8_byte.o(i.i2c_read_scl_and_delay) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.i2c_read_scl_and_delay) refers to u8x8_byte.o(i.i2c_delay) for i2c_delay
    u8x8_byte.o(i.i2c_read_sda) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.i2c_start) refers to u8x8_byte.o(i.i2c_read_sda) for i2c_read_sda
    u8x8_byte.o(i.i2c_start) refers to u8x8_byte.o(i.i2c_delay) for i2c_delay
    u8x8_byte.o(i.i2c_start) refers to u8x8_byte.o(i.i2c_read_scl_and_delay) for i2c_read_scl_and_delay
    u8x8_byte.o(i.i2c_start) refers to u8x8_byte.o(i.i2c_clear_sda) for i2c_clear_sda
    u8x8_byte.o(i.i2c_start) refers to u8x8_byte.o(i.i2c_clear_scl) for i2c_clear_scl
    u8x8_byte.o(i.i2c_stop) refers to u8x8_byte.o(i.i2c_clear_sda) for i2c_clear_sda
    u8x8_byte.o(i.i2c_stop) refers to u8x8_byte.o(i.i2c_delay) for i2c_delay
    u8x8_byte.o(i.i2c_stop) refers to u8x8_byte.o(i.i2c_read_scl_and_delay) for i2c_read_scl_and_delay
    u8x8_byte.o(i.i2c_stop) refers to u8x8_byte.o(i.i2c_read_sda) for i2c_read_sda
    u8x8_byte.o(i.i2c_write_bit) refers to u8x8_byte.o(i.i2c_read_sda) for i2c_read_sda
    u8x8_byte.o(i.i2c_write_bit) refers to u8x8_byte.o(i.i2c_clear_sda) for i2c_clear_sda
    u8x8_byte.o(i.i2c_write_bit) refers to u8x8_byte.o(i.i2c_delay) for i2c_delay
    u8x8_byte.o(i.i2c_write_bit) refers to u8x8_byte.o(i.i2c_read_scl_and_delay) for i2c_read_scl_and_delay
    u8x8_byte.o(i.i2c_write_bit) refers to u8x8_byte.o(i.i2c_clear_scl) for i2c_clear_scl
    u8x8_byte.o(i.i2c_write_byte) refers to u8x8_byte.o(i.i2c_write_bit) for i2c_write_bit
    u8x8_byte.o(i.i2c_write_byte) refers to u8x8_byte.o(i.i2c_read_bit) for i2c_read_bit
    u8x8_byte.o(i.u8x8_byte_3wire_sw_spi) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.u8x8_byte_3wire_sw_spi) refers to u8x8_byte.o(.data) for last_dc
    u8x8_byte.o(i.u8x8_byte_4wire_sw_spi) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.u8x8_byte_8bit_6800mode) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.u8x8_byte_8bit_8080mode) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.u8x8_byte_SendByte) refers to u8x8_byte.o(i.u8x8_byte_SendBytes) for u8x8_byte_SendBytes
    u8x8_byte.o(i.u8x8_byte_ks0108) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.u8x8_byte_ks0108) refers to u8x8_byte.o(i.u8x8_byte_set_ks0108_cs) for u8x8_byte_set_ks0108_cs
    u8x8_byte.o(i.u8x8_byte_sed1520) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.u8x8_byte_sed1520) refers to u8x8_byte.o(.data) for enable_pin
    u8x8_byte.o(i.u8x8_byte_set_ks0108_cs) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.u8x8_byte_sw_i2c) refers to u8x8_byte.o(i.i2c_write_byte) for i2c_write_byte
    u8x8_byte.o(i.u8x8_byte_sw_i2c) refers to u8x8_byte.o(i.i2c_init) for i2c_init
    u8x8_byte.o(i.u8x8_byte_sw_i2c) refers to u8x8_byte.o(i.i2c_start) for i2c_start
    u8x8_byte.o(i.u8x8_byte_sw_i2c) refers to u8x8_byte.o(i.i2c_stop) for i2c_stop
    u8x8_cad.o(i.u8x8_SendF) refers to u8x8_cad.o(i.u8x8_cad_vsendf) for u8x8_cad_vsendf
    u8x8_cad.o(i.u8x8_cad_001) refers to u8x8_byte.o(i.u8x8_byte_SetDC) for u8x8_byte_SetDC
    u8x8_cad.o(i.u8x8_cad_001) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_011) refers to u8x8_byte.o(i.u8x8_byte_SetDC) for u8x8_byte_SetDC
    u8x8_cad.o(i.u8x8_cad_011) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_011_ssd13xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_cad_011_ssd13xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_011_ssd13xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_cad_011_ssd13xx_i2c) refers to u8x8_cad.o(i.u8x8_i2c_data_transfer) for u8x8_i2c_data_transfer
    u8x8_cad.o(i.u8x8_cad_100) refers to u8x8_byte.o(i.u8x8_byte_SetDC) for u8x8_byte_SetDC
    u8x8_cad.o(i.u8x8_cad_100) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_110) refers to u8x8_byte.o(i.u8x8_byte_SetDC) for u8x8_byte_SetDC
    u8x8_cad.o(i.u8x8_cad_110) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_SendSequence) refers to u8x8_cad.o(i.u8x8_cad_SendData) for u8x8_cad_SendData
    u8x8_cad.o(i.u8x8_cad_SendSequence) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_cad.o(i.u8x8_cad_empty) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_ld7032_i2c) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_cad_ld7032_i2c) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_cad_ld7032_i2c) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_ld7032_i2c) refers to u8x8_cad.o(.data) for in_transfer
    u8x8_cad.o(i.u8x8_cad_ssd13xx_fast_i2c) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_cad_ssd13xx_fast_i2c) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_cad_ssd13xx_fast_i2c) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_ssd13xx_fast_i2c) refers to u8x8_cad.o(i.u8x8_i2c_data_transfer) for u8x8_i2c_data_transfer
    u8x8_cad.o(i.u8x8_cad_ssd13xx_fast_i2c) refers to u8x8_cad.o(.data) for in_transfer
    u8x8_cad.o(i.u8x8_cad_ssd13xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_cad_ssd13xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_ssd13xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_cad_ssd13xx_i2c) refers to u8x8_cad.o(i.u8x8_i2c_data_transfer) for u8x8_i2c_data_transfer
    u8x8_cad.o(i.u8x8_cad_st75256_i2c) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_cad_st75256_i2c) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_st75256_i2c) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_cad_st75256_i2c) refers to u8x8_cad.o(i.u8x8_i2c_data_transfer) for u8x8_i2c_data_transfer
    u8x8_cad.o(i.u8x8_cad_st7920_spi) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_st7920_spi) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_cad.o(i.u8x8_cad_st7920_spi) refers to u8x8_byte.o(i.u8x8_byte_SendBytes) for u8x8_byte_SendBytes
    u8x8_cad.o(i.u8x8_cad_st7920_spi) refers to u8x8_cad.o(.bss) for buf
    u8x8_cad.o(i.u8x8_cad_uc1638_i2c) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_cad_uc1638_i2c) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_cad_uc1638_i2c) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_uc1638_i2c) refers to u8x8_cad.o(.data) for in_transfer
    u8x8_cad.o(i.u8x8_cad_uc16xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_cad_uc16xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_cad_uc16xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_uc16xx_i2c) refers to u8x8_cad.o(.data) for in_transfer
    u8x8_cad.o(i.u8x8_cad_vsendf) refers to u8x8_cad.o(i.u8x8_cad_StartTransfer) for u8x8_cad_StartTransfer
    u8x8_cad.o(i.u8x8_cad_vsendf) refers to u8x8_cad.o(i.u8x8_cad_SendArg) for u8x8_cad_SendArg
    u8x8_cad.o(i.u8x8_cad_vsendf) refers to u8x8_cad.o(i.u8x8_cad_SendCmd) for u8x8_cad_SendCmd
    u8x8_cad.o(i.u8x8_cad_vsendf) refers to u8x8_cad.o(i.u8x8_cad_SendData) for u8x8_cad_SendData
    u8x8_cad.o(i.u8x8_cad_vsendf) refers to u8x8_cad.o(i.u8x8_cad_EndTransfer) for u8x8_cad_EndTransfer
    u8x8_cad.o(i.u8x8_gu800_cad_110) refers to u8x8_byte.o(i.u8x8_byte_SetDC) for u8x8_byte_SetDC
    u8x8_cad.o(i.u8x8_gu800_cad_110) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_gu800_cad_110) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_gu800_cad_110) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_i2c_data_transfer) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_i2c_data_transfer) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_i2c_data_transfer) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_capture.o(i.u8x8_capture_write_pbm_pre) refers to u8x8_u16toa.o(i.u8x8_utoa) for u8x8_utoa
    u8x8_capture.o(i.u8x8_capture_write_xbm_pre) refers to u8x8_u16toa.o(i.u8x8_utoa) for u8x8_utoa
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_128x64_noname) refers to u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_generic) for u8x8_d_ssd1315_generic
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_128x64_noname) refers to u8x8_display.o(i.u8x8_d_helper_display_init) for u8x8_d_helper_display_init
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_128x64_noname) refers to u8x8_cad.o(i.u8x8_cad_SendSequence) for u8x8_cad_SendSequence
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_128x64_noname) refers to u8x8_display.o(i.u8x8_d_helper_display_setup_memory) for u8x8_d_helper_display_setup_memory
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_128x64_noname) refers to u8x8_d_ssd1315_128x64_noname.o(.constdata) for u8x8_d_ssd1315_128x64_noname_init_seq
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_generic) refers to u8x8_cad.o(i.u8x8_cad_SendSequence) for u8x8_cad_SendSequence
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_generic) refers to u8x8_cad.o(i.u8x8_cad_StartTransfer) for u8x8_cad_StartTransfer
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_generic) refers to u8x8_cad.o(i.u8x8_cad_SendCmd) for u8x8_cad_SendCmd
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_generic) refers to u8x8_cad.o(i.u8x8_cad_SendArg) for u8x8_cad_SendArg
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_generic) refers to u8x8_cad.o(i.u8x8_cad_EndTransfer) for u8x8_cad_EndTransfer
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_generic) refers to u8x8_cad.o(i.u8x8_cad_SendData) for u8x8_cad_SendData
    u8x8_d_ssd1315_128x64_noname.o(i.u8x8_d_ssd1315_generic) refers to u8x8_d_ssd1315_128x64_noname.o(.constdata) for u8x8_d_ssd1315_128x64_noname_powersave0_seq
    u8x8_debounce.o(i.u8x8_GetMenuEvent) refers to u8x8_debounce.o(i.u8x8_read_pin_state) for u8x8_read_pin_state
    u8x8_debounce.o(i.u8x8_GetMenuEvent) refers to u8x8_debounce.o(i.u8x8_find_first_diff) for u8x8_find_first_diff
    u8x8_debounce.o(i.u8x8_read_pin_state) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_display.o(i.u8x8_ClearDisplay) refers to u8x8_display.o(i.u8x8_ClearDisplayWithTile) for u8x8_ClearDisplayWithTile
    u8x8_display.o(i.u8x8_FillDisplay) refers to u8x8_display.o(i.u8x8_ClearDisplayWithTile) for u8x8_ClearDisplayWithTile
    u8x8_display.o(i.u8x8_d_helper_display_init) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_input_value.o(i.u8x8_UserInterfaceInputValue) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8x8_input_value.o(i.u8x8_UserInterfaceInputValue) refers to u8x8_8x8.o(i.u8x8_GetUTF8Len) for u8x8_GetUTF8Len
    u8x8_input_value.o(i.u8x8_UserInterfaceInputValue) refers to u8x8_display.o(i.u8x8_ClearDisplay) for u8x8_ClearDisplay
    u8x8_input_value.o(i.u8x8_UserInterfaceInputValue) refers to u8x8_string.o(i.u8x8_DrawUTF8Lines) for u8x8_DrawUTF8Lines
    u8x8_input_value.o(i.u8x8_UserInterfaceInputValue) refers to u8x8_8x8.o(i.u8x8_DrawUTF8) for u8x8_DrawUTF8
    u8x8_input_value.o(i.u8x8_UserInterfaceInputValue) refers to u8x8_u8toa.o(i.u8x8_u8toa) for u8x8_u8toa
    u8x8_input_value.o(i.u8x8_UserInterfaceInputValue) refers to u8x8_debounce.o(i.u8x8_GetMenuEvent) for u8x8_GetMenuEvent
    u8x8_message.o(i.u8x8_UserInterfaceMessage) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8x8_message.o(i.u8x8_UserInterfaceMessage) refers to u8x8_display.o(i.u8x8_ClearDisplay) for u8x8_ClearDisplay
    u8x8_message.o(i.u8x8_UserInterfaceMessage) refers to u8x8_string.o(i.u8x8_DrawUTF8Lines) for u8x8_DrawUTF8Lines
    u8x8_message.o(i.u8x8_UserInterfaceMessage) refers to u8x8_string.o(i.u8x8_DrawUTF8Line) for u8x8_DrawUTF8Line
    u8x8_message.o(i.u8x8_UserInterfaceMessage) refers to u8x8_message.o(i.u8x8_draw_button_line) for u8x8_draw_button_line
    u8x8_message.o(i.u8x8_UserInterfaceMessage) refers to u8x8_debounce.o(i.u8x8_GetMenuEvent) for u8x8_GetMenuEvent
    u8x8_message.o(i.u8x8_draw_button_line) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8x8_message.o(i.u8x8_draw_button_line) refers to u8x8_string.o(i.u8x8_GetStringLineStart) for u8x8_GetStringLineStart
    u8x8_message.o(i.u8x8_draw_button_line) refers to u8x8_8x8.o(i.u8x8_GetUTF8Len) for u8x8_GetUTF8Len
    u8x8_message.o(i.u8x8_draw_button_line) refers to u8x8_8x8.o(i.u8x8_DrawUTF8) for u8x8_DrawUTF8
    u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList) refers to u8x8_string.o(i.u8x8_DrawUTF8Lines) for u8x8_DrawUTF8Lines
    u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList) refers to u8x8_selection_list.o(i.u8x8_DrawSelectionList) for u8x8_DrawSelectionList
    u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList) refers to u8x8_debounce.o(i.u8x8_GetMenuEvent) for u8x8_GetMenuEvent
    u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList) refers to u8x8_selection_list.o(i.u8sl_Next) for u8sl_Next
    u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList) refers to u8x8_selection_list.o(i.u8sl_Prev) for u8sl_Prev
    u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList) refers to u8x8_selection_list.o(i.u8x8_sl_string_line_cb) for u8x8_sl_string_line_cb
    u8x8_selection_list.o(i.u8x8_sl_string_line_cb) refers to u8x8_string.o(i.u8x8_GetStringLineStart) for u8x8_GetStringLineStart
    u8x8_selection_list.o(i.u8x8_sl_string_line_cb) refers to u8x8_string.o(i.u8x8_DrawUTF8Line) for u8x8_DrawUTF8Line
    u8x8_setup.o(i.u8x8_Setup) refers to u8x8_setup.o(i.u8x8_SetupDefaults) for u8x8_SetupDefaults
    u8x8_setup.o(i.u8x8_Setup) refers to u8x8_display.o(i.u8x8_SetupMemory) for u8x8_SetupMemory
    u8x8_setup.o(i.u8x8_SetupDefaults) refers to u8x8_setup.o(i.u8x8_dummy_cb) for u8x8_dummy_cb
    u8x8_setup.o(i.u8x8_d_null_cb) refers to u8x8_display.o(i.u8x8_d_helper_display_setup_memory) for u8x8_d_helper_display_setup_memory
    u8x8_setup.o(i.u8x8_d_null_cb) refers to u8x8_display.o(i.u8x8_d_helper_display_init) for u8x8_d_helper_display_init
    u8x8_setup.o(i.u8x8_d_null_cb) refers to u8x8_setup.o(.constdata) for u8x8_null_display_info
    u8x8_string.o(i.u8x8_CopyStringLine) refers to u8x8_string.o(i.u8x8_GetStringLineStart) for u8x8_GetStringLineStart
    u8x8_string.o(i.u8x8_DrawUTF8Line) refers to u8x8_8x8.o(i.u8x8_GetUTF8Len) for u8x8_GetUTF8Len
    u8x8_string.o(i.u8x8_DrawUTF8Line) refers to u8x8_8x8.o(i.u8x8_DrawUTF8) for u8x8_DrawUTF8
    u8x8_string.o(i.u8x8_DrawUTF8Lines) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8x8_string.o(i.u8x8_DrawUTF8Lines) refers to u8x8_string.o(i.u8x8_GetStringLineStart) for u8x8_GetStringLineStart
    u8x8_string.o(i.u8x8_DrawUTF8Lines) refers to u8x8_string.o(i.u8x8_DrawUTF8Line) for u8x8_DrawUTF8Line
    u8x8_u8toa.o(i.u8x8_s8toa) refers to u8x8_u8toa.o(i.u8x8_u8toap) for u8x8_u8toap
    u8x8_u8toa.o(i.u8x8_s8toa) refers to u8x8_u8toa.o(.data) for buf
    u8x8_u8toa.o(i.u8x8_u8toa) refers to u8x8_u8toa.o(i.u8x8_u8toap) for u8x8_u8toap
    u8x8_u8toa.o(i.u8x8_u8toa) refers to u8x8_u8toa.o(.data) for buf
    u8x8_u8toa.o(i.u8x8_u8toap) refers to u8x8_u8toa.o(.constdata) for u8x8_u8toa_tab
    u8x8_u8toa.o(i.u8x8_u8tox) refers to memcpya.o(.text) for __aeabi_memcpy4
    u8x8_u8toa.o(i.u8x8_u8tox) refers to u8x8_u8toa.o(.data) for buf
    u8x8_u16toa.o(i.u8x8_u16toa) refers to u8x8_u16toa.o(i.u8x8_u16toap) for u8x8_u16toap
    u8x8_u16toa.o(i.u8x8_u16toa) refers to u8x8_u16toa.o(.data) for buf
    u8x8_u16toa.o(i.u8x8_utoa) refers to u8x8_u16toa.o(i.u8x8_u16toa) for u8x8_u16toa
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cfcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f103xe.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f103xe.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f103xe.o(HEAP), (512 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (60 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (60 bytes).
    Removing stm32f1xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_ConfigEventout), (24 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_DisableEventout), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_EnableEventout), (20 bytes).
    Removing stm32f1xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_DeInit), (60 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (70 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (224 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (70 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetError), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetMode), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetState), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (440 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (126 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (876 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (452 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (244 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (676 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (364 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (548 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (288 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (452 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (232 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (884 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (604 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (264 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (532 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (244 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (384 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (300 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (152 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (460 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (158 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (460 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (158 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (404 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (300 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (152 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_DMAError), (70 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt), (318 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead), (312 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead), (352 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Slave_AF), (168 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (138 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout), (98 bytes).
    Removing stm32f1xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (20 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (20 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (20 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (20 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (20 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (20 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DeInit), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_ResumeTick), (18 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SetTickFreq), (48 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SuspendTick), (18 bytes).
    Removing stm32f1xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit), (272 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (68 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (200 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (100 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (32 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (84 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (280 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (304 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.constdata), (18 bytes).
    Removing stm32f1xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit), (340 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (28 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_LockPin), (46 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (20 bytes).
    Removing stm32f1xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit), (140 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetError), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (1620 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (90 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start), (114 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (106 bytes).
    Removing stm32f1xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (68 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetActive), (48 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (48 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (148 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (68 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (40 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (180 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DeInit), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (20 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (20 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (20 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (20 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (40 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (84 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe), (6 bytes).
    Removing stm32f1xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord), (32 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode), (132 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation), (112 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (376 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (8 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (44 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program), (176 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT), (100 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock), (48 bytes).
    Removing stm32f1xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase), (40 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (232 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (236 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (28 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData), (76 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (124 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase), (40 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (80 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase), (96 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (28 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetUserData), (40 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (200 bytes).
    Removing stm32f1xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (120 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearPending), (28 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (156 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetPending), (36 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (44 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (240 bytes).
    Removing stm32f1xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (68 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (68 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init), (122 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_Init), (146 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_SendBreak), (80 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (82 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (82 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init), (158 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (6 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (296 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (114 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort), (278 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive), (194 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (216 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit), (124 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (144 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT), (328 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAPause), (166 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAResume), (170 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DeInit), (62 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetError), (6 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetState), (20 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive), (194 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (140 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_IT), (56 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback), (56 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (28 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt), (88 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback), (56 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt), (14 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (24 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT), (64 bytes).
    Removing system_stm32f1xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f1xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f1xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f1xx.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing key_app.o(.rev16_text), (4 bytes).
    Removing key_app.o(.revsh_text), (4 bytes).
    Removing key_app.o(.rrx_text), (6 bytes).
    Removing led_app.o(.rev16_text), (4 bytes).
    Removing led_app.o(.revsh_text), (4 bytes).
    Removing led_app.o(.rrx_text), (6 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing scheduler.o(.rrx_text), (6 bytes).
    Removing btn_app.o(.rev16_text), (4 bytes).
    Removing btn_app.o(.revsh_text), (4 bytes).
    Removing btn_app.o(.rrx_text), (6 bytes).
    Removing usart_app.o(.rev16_text), (4 bytes).
    Removing usart_app.o(.revsh_text), (4 bytes).
    Removing usart_app.o(.rrx_text), (6 bytes).
    Removing oled_app.o(.rev16_text), (4 bytes).
    Removing oled_app.o(.revsh_text), (4 bytes).
    Removing oled_app.o(.rrx_text), (6 bytes).
    Removing oled_app.o(i.oled_printf), (60 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_add_btn), (32 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_remove_btn), (32 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_remove_btn_by_idx), (32 bytes).
    Removing ebtn.o(i.ebtn_combo_register), (60 bytes).
    Removing ebtn.o(i.ebtn_get_btn_by_key_id), (80 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_btn), (12 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_btn_dyn), (12 bytes).
    Removing ebtn.o(i.ebtn_get_config), (12 bytes).
    Removing ebtn.o(i.ebtn_get_total_btn_cnt), (28 bytes).
    Removing ebtn.o(i.ebtn_is_btn_active), (20 bytes).
    Removing ebtn.o(i.ebtn_is_btn_in_process), (20 bytes).
    Removing ebtn.o(i.ebtn_is_in_process), (132 bytes).
    Removing ebtn.o(i.ebtn_register), (72 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_getchar), (160 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_peek), (172 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_put_force), (304 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar), (164 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar_force), (200 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_reset), (112 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_Allfill), (56 bytes).
    Removing oled.o(i.OLED_Display_Off), (22 bytes).
    Removing oled.o(i.OLED_Display_On), (22 bytes).
    Removing oled.o(i.OLED_Pow), (22 bytes).
    Removing oled.o(i.OLED_ShowChar), (156 bytes).
    Removing oled.o(i.OLED_ShowFloat), (328 bytes).
    Removing oled.o(i.OLED_ShowHanzi), (100 bytes).
    Removing oled.o(i.OLED_ShowHzbig), (184 bytes).
    Removing oled.o(i.OLED_ShowNum), (136 bytes).
    Removing oled.o(i.OLED_ShowPic), (76 bytes).
    Removing oled.o(i.OLED_ShowStr), (58 bytes).
    Removing oled.o(.constdata), (2712 bytes).
    Removing mui.o(i.mui_Draw), (20 bytes).
    Removing mui.o(i.mui_EnterForm), (96 bytes).
    Removing mui.o(i.mui_GetCurrentCursorFocusPosition), (28 bytes).
    Removing mui.o(i.mui_GetCurrentFormId), (32 bytes).
    Removing mui.o(i.mui_GetSelectableFieldOptionCnt), (48 bytes).
    Removing mui.o(i.mui_GetSelectableFieldTextOption), (52 bytes).
    Removing mui.o(i.mui_GotoForm), (42 bytes).
    Removing mui.o(i.mui_GotoFormAutoCursorPosition), (44 bytes).
    Removing mui.o(i.mui_Init), (44 bytes).
    Removing mui.o(i.mui_LeaveForm), (52 bytes).
    Removing mui.o(i.mui_NextField), (46 bytes).
    Removing mui.o(i.mui_PrevField), (76 bytes).
    Removing mui.o(i.mui_RestoreForm), (58 bytes).
    Removing mui.o(i.mui_SaveCursorPosition), (92 bytes).
    Removing mui.o(i.mui_SaveForm), (22 bytes).
    Removing mui.o(i.mui_SaveFormWithCursorPosition), (114 bytes).
    Removing mui.o(i.mui_SendSelect), (14 bytes).
    Removing mui.o(i.mui_SendSelectWithExecuteOnSelectFieldSearch), (60 bytes).
    Removing mui.o(i.mui_SendValueDecrement), (14 bytes).
    Removing mui.o(i.mui_SendValueIncrement), (14 bytes).
    Removing mui.o(i.mui_fds_first_token), (44 bytes).
    Removing mui.o(i.mui_fds_get_cmd_size), (74 bytes).
    Removing mui.o(i.mui_fds_get_cmd_size_without_text), (120 bytes).
    Removing mui.o(i.mui_fds_get_nth_token), (40 bytes).
    Removing mui.o(i.mui_fds_get_token_cnt), (34 bytes).
    Removing mui.o(i.mui_fds_next_token), (86 bytes).
    Removing mui.o(i.mui_fds_parse_text), (92 bytes).
    Removing mui.o(i.mui_find_form), (62 bytes).
    Removing mui.o(i.mui_find_uif), (58 bytes).
    Removing mui.o(i.mui_get_fds_char), (6 bytes).
    Removing mui.o(i.mui_inner_loop_over_form), (68 bytes).
    Removing mui.o(i.mui_loop_over_form), (42 bytes).
    Removing mui.o(i.mui_next_field), (44 bytes).
    Removing mui.o(i.mui_prepare_current_field), (380 bytes).
    Removing mui.o(i.mui_send_cursor_enter_msg), (20 bytes).
    Removing mui.o(i.mui_send_cursor_msg), (38 bytes).
    Removing mui.o(i.mui_task_draw), (18 bytes).
    Removing mui.o(i.mui_task_find_execute_on_select_field), (24 bytes).
    Removing mui.o(i.mui_task_find_first_cursor_uif), (24 bytes).
    Removing mui.o(i.mui_task_find_last_cursor_uif), (20 bytes).
    Removing mui.o(i.mui_task_find_next_cursor_uif), (44 bytes).
    Removing mui.o(i.mui_task_find_prev_cursor_uif), (36 bytes).
    Removing mui.o(i.mui_task_form_end), (18 bytes).
    Removing mui.o(i.mui_task_form_start), (18 bytes).
    Removing mui.o(i.mui_task_get_current_cursor_focus_position), (38 bytes).
    Removing mui.o(i.mui_uif_is_cursor_selectable), (20 bytes).
    Removing mui_u8g2.o(i.mui_get_U8g2), (6 bytes).
    Removing mui_u8g2.o(i.mui_get_arg), (8 bytes).
    Removing mui_u8g2.o(i.mui_get_text), (8 bytes).
    Removing mui_u8g2.o(i.mui_get_x), (32 bytes).
    Removing mui_u8g2.o(i.mui_get_y), (8 bytes).
    Removing mui_u8g2.o(i.mui_hline), (52 bytes).
    Removing mui_u8g2.o(i.mui_is_valid_char), (50 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_back_w1_fi), (112 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_back_w1_pi), (108 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_back_w2_fi), (114 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_back_w2_if), (114 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_back_wm_fi), (90 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_back_wm_if), (90 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_exit_wm_fi), (110 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_goto_w1_fi), (122 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_goto_w1_pi), (118 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_goto_w2_fi), (124 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_goto_w2_if), (124 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_goto_wm_fi), (100 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_btn_goto_wm_if), (100 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_draw_button_fi), (42 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_draw_button_if), (42 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_draw_button_pf), (42 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_draw_button_pi), (42 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_draw_button_utf), (72 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_draw_text), (92 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_get_fi_flags), (28 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_get_if_flags), (42 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_get_pf_flags), (30 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_get_pi_flags), (30 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_goto_data), (48 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf), (184 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi), (180 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_handle_scroll_next_prev_events), (136 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_s8_min_max_wm_mse_pf), (118 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_s8_min_max_wm_mse_pi), (118 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_s8_min_max_wm_mud_pf), (160 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_s8_min_max_wm_mud_pi), (160 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pf), (164 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pi), (168 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_set_font_style_function), (32 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u16_list_child_mse_common), (154 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u16_list_child_w1_pi), (204 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u16_list_goto_w1_pi), (200 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u16_list_line_wa_mse_pi), (138 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u16_list_line_wa_mud_pi), (174 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u16_list_parent_wm_pi), (108 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm), (388 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_bar_mse_msg_handler), (126 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_bar_mud_msg_handler), (166 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mse_pf), (46 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mse_pi), (46 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mud_pf), (46 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mud_pi), (46 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_char_wm_mud_pi), (192 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_chkbox_wm_pi), (292 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mse_pf), (46 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mse_pi), (46 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mud_pf), (46 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mud_pi), (46 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_min_max_wm_mse_pf), (110 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_min_max_wm_mse_pi), (110 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_min_max_wm_mud_pf), (152 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_min_max_wm_mud_pi), (152 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common), (138 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_opt_child_wm_pi), (128 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mse_pf), (138 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mse_pi), (138 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mud_pf), (174 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mud_pi), (174 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_opt_parent_wm_pi), (110 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi), (190 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi), (208 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_radio_wm_pi), (284 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pf), (132 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pi), (132 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_x8_min_max_wm_mse_pf), (110 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_x8_min_max_wm_mud_pf), (152 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_x8_min_max_wm_mud_pi), (152 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pf), (120 bytes).
    Removing mui_u8g2.o(i.mui_u8g2_x8_vmm_draw_wm_pi), (120 bytes).
    Removing mui_u8g2.o(i.mui_x8g2_x8_min_max_wm_mse_pi), (110 bytes).
    Removing mui_u8g2.o(i.u8g2_DrawCheckbox), (66 bytes).
    Removing mui_u8g2.o(i.u8g2_DrawValueMark), (28 bytes).
    Removing mui_u8g2.o(.constdata), (6 bytes).
    Removing u8g2_arc.o(i.u8g2_DrawArc), (70 bytes).
    Removing u8g2_arc.o(i.u8g2_draw_arc), (600 bytes).
    Removing u8g2_bitmap.o(i.u8g2_DrawBitmap), (90 bytes).
    Removing u8g2_bitmap.o(i.u8g2_DrawHXBM), (232 bytes).
    Removing u8g2_bitmap.o(i.u8g2_DrawHXBMP), (232 bytes).
    Removing u8g2_bitmap.o(i.u8g2_DrawHorizontalBitmap), (152 bytes).
    Removing u8g2_bitmap.o(i.u8g2_DrawXBM), (88 bytes).
    Removing u8g2_bitmap.o(i.u8g2_DrawXBMP), (88 bytes).
    Removing u8g2_bitmap.o(i.u8g2_SetBitmapMode), (6 bytes).
    Removing u8g2_box.o(i.u8g2_DrawBox), (74 bytes).
    Removing u8g2_box.o(i.u8g2_DrawFrame), (138 bytes).
    Removing u8g2_box.o(i.u8g2_DrawRBox), (318 bytes).
    Removing u8g2_box.o(i.u8g2_DrawRFrame), (332 bytes).
    Removing u8g2_buffer.o(i.u8g2_FirstPage), (26 bytes).
    Removing u8g2_buffer.o(i.u8g2_NextPage), (64 bytes).
    Removing u8g2_buffer.o(i.u8g2_SetBufferCurrTileRow), (28 bytes).
    Removing u8g2_buffer.o(i.u8g2_UpdateDisplay), (12 bytes).
    Removing u8g2_buffer.o(i.u8g2_UpdateDisplayArea), (86 bytes).
    Removing u8g2_buffer.o(i.u8g2_WriteBufferPBM), (44 bytes).
    Removing u8g2_buffer.o(i.u8g2_WriteBufferPBM2), (44 bytes).
    Removing u8g2_buffer.o(i.u8g2_WriteBufferXBM), (44 bytes).
    Removing u8g2_buffer.o(i.u8g2_WriteBufferXBM2), (44 bytes).
    Removing u8g2_button.o(i.u8g2_DrawButtonFrame), (348 bytes).
    Removing u8g2_button.o(i.u8g2_DrawButtonUTF8), (124 bytes).
    Removing u8g2_circle.o(i.u8g2_DrawDisc), (70 bytes).
    Removing u8g2_circle.o(i.u8g2_DrawEllipse), (70 bytes).
    Removing u8g2_circle.o(i.u8g2_DrawFilledEllipse), (70 bytes).
    Removing u8g2_circle.o(i.u8g2_draw_disc), (128 bytes).
    Removing u8g2_circle.o(i.u8g2_draw_disc_section), (180 bytes).
    Removing u8g2_circle.o(i.u8g2_draw_ellipse), (294 bytes).
    Removing u8g2_circle.o(i.u8g2_draw_ellipse_section), (100 bytes).
    Removing u8g2_circle.o(i.u8g2_draw_filled_ellipse), (294 bytes).
    Removing u8g2_circle.o(i.u8g2_draw_filled_ellipse_section), (116 bytes).
    Removing u8g2_cleardisplay.o(i.u8g2_ClearDisplay), (32 bytes).
    Removing u8g2_font.o(i.u8g2_DrawExtUTF8), (180 bytes).
    Removing u8g2_font.o(i.u8g2_DrawExtendedUTF8), (188 bytes).
    Removing u8g2_font.o(i.u8g2_DrawGlyphX2), (42 bytes).
    Removing u8g2_font.o(i.u8g2_DrawHB), (78 bytes).
    Removing u8g2_font.o(i.u8g2_DrawStrX2), (36 bytes).
    Removing u8g2_font.o(i.u8g2_DrawUTF8), (36 bytes).
    Removing u8g2_font.o(i.u8g2_DrawUTF8X2), (36 bytes).
    Removing u8g2_font.o(i.u8g2_GetFontBBXHeight), (8 bytes).
    Removing u8g2_font.o(i.u8g2_GetFontBBXOffX), (8 bytes).
    Removing u8g2_font.o(i.u8g2_GetFontBBXOffY), (8 bytes).
    Removing u8g2_font.o(i.u8g2_GetFontBBXWidth), (8 bytes).
    Removing u8g2_font.o(i.u8g2_GetFontCapitalAHeight), (8 bytes).
    Removing u8g2_font.o(i.u8g2_GetFontSize), (70 bytes).
    Removing u8g2_font.o(i.u8g2_GetGlyphHorizontalProperties), (92 bytes).
    Removing u8g2_font.o(i.u8g2_GetGlyphWidth), (72 bytes).
    Removing u8g2_font.o(i.u8g2_GetStrWidth), (24 bytes).
    Removing u8g2_font.o(i.u8g2_GetStrX), (34 bytes).
    Removing u8g2_font.o(i.u8g2_GetUTF8Width), (24 bytes).
    Removing u8g2_font.o(i.u8g2_GetXOffsetGlyph), (20 bytes).
    Removing u8g2_font.o(i.u8g2_GetXOffsetUTF8), (62 bytes).
    Removing u8g2_font.o(i.u8g2_IsAllValidUTF8), (24 bytes).
    Removing u8g2_font.o(i.u8g2_IsGlyph), (24 bytes).
    Removing u8g2_font.o(i.u8g2_SetFontDirection), (6 bytes).
    Removing u8g2_font.o(i.u8g2_SetFontMode), (6 bytes).
    Removing u8g2_font.o(i.u8g2_SetFontPosBottom), (12 bytes).
    Removing u8g2_font.o(i.u8g2_SetFontPosCenter), (12 bytes).
    Removing u8g2_font.o(i.u8g2_SetFontPosTop), (12 bytes).
    Removing u8g2_font.o(i.u8g2_SetFontRefHeightAll), (18 bytes).
    Removing u8g2_font.o(i.u8g2_SetFontRefHeightExtendedText), (18 bytes).
    Removing u8g2_font.o(i.u8g2_SetFontRefHeightText), (18 bytes).
    Removing u8g2_font.o(i.u8g2_draw_string_2x), (94 bytes).
    Removing u8g2_font.o(i.u8g2_font_2x_decode_glyph), (246 bytes).
    Removing u8g2_font.o(i.u8g2_font_2x_decode_len), (212 bytes).
    Removing u8g2_font.o(i.u8g2_font_2x_draw_glyph), (58 bytes).
    Removing u8g2_font.o(i.u8g2_font_calc_vref_bottom), (10 bytes).
    Removing u8g2_font.o(i.u8g2_font_calc_vref_center), (32 bytes).
    Removing u8g2_font.o(i.u8g2_font_calc_vref_top), (14 bytes).
    Removing u8g2_font.o(i.u8g2_is_all_valid), (64 bytes).
    Removing u8g2_font.o(i.u8g2_string_width), (138 bytes).
    Removing u8g2_hvline.o(i.u8g2_DrawHLine), (28 bytes).
    Removing u8g2_hvline.o(i.u8g2_DrawVLine), (28 bytes).
    Removing u8g2_input_value.o(i.u8g2_UserInterfaceInputValue), (392 bytes).
    Removing u8g2_kerning.o(i.u8g2_GetKerning), (112 bytes).
    Removing u8g2_kerning.o(i.u8g2_GetKerningByTable), (68 bytes).
    Removing u8g2_line.o(i.u8g2_DrawLine), (214 bytes).
    Removing u8g2_ll_hvline.o(i.u8g2_ll_hvline_horizontal_right_lsb), (190 bytes).
    Removing u8g2_message.o(i.u8g2_UserInterfaceMessage), (368 bytes).
    Removing u8g2_message.o(i.u8g2_draw_button_line), (190 bytes).
    Removing u8g2_polygon.o(i.pg_AddPolygonXY), (28 bytes).
    Removing u8g2_polygon.o(i.pg_ClearPolygonXY), (6 bytes).
    Removing u8g2_polygon.o(i.pg_DrawPolygon), (28 bytes).
    Removing u8g2_polygon.o(i.pg_dec), (22 bytes).
    Removing u8g2_polygon.o(i.pg_exec), (110 bytes).
    Removing u8g2_polygon.o(i.pg_expand_min_y), (84 bytes).
    Removing u8g2_polygon.o(i.pg_hline), (164 bytes).
    Removing u8g2_polygon.o(i.pg_inc), (18 bytes).
    Removing u8g2_polygon.o(i.pg_line_init), (78 bytes).
    Removing u8g2_polygon.o(i.pg_prepare), (192 bytes).
    Removing u8g2_polygon.o(i.pge_Init), (90 bytes).
    Removing u8g2_polygon.o(i.pge_Next), (78 bytes).
    Removing u8g2_polygon.o(i.u8g2_AddPolygonXY), (24 bytes).
    Removing u8g2_polygon.o(i.u8g2_ClearPolygonXY), (16 bytes).
    Removing u8g2_polygon.o(i.u8g2_DrawPolygon), (20 bytes).
    Removing u8g2_polygon.o(i.u8g2_DrawTriangle), (64 bytes).
    Removing u8g2_polygon.o(.bss), (76 bytes).
    Removing u8g2_selection_list.o(i.u8g2_DrawSelectionList), (52 bytes).
    Removing u8g2_selection_list.o(i.u8g2_DrawUTF8Line), (252 bytes).
    Removing u8g2_selection_list.o(i.u8g2_DrawUTF8Lines), (88 bytes).
    Removing u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList), (352 bytes).
    Removing u8g2_selection_list.o(i.u8g2_draw_selection_list_line), (96 bytes).
    Removing u8g2_setup.o(i.u8g2_SendF), (30 bytes).
    Removing u8g2_setup.o(i.u8g2_SetClipWindow), (42 bytes).
    Removing u8g2_setup.o(i.u8g2_SetDisplayRotation), (26 bytes).
    Removing u8g2_setup.o(i.u8g2_Setup_null), (56 bytes).
    Removing u8g2_setup.o(.data), (8 bytes).
    Removing u8log.o(i.u8log_Init), (36 bytes).
    Removing u8log.o(i.u8log_SetCallback), (6 bytes).
    Removing u8log.o(i.u8log_SetLineHeightOffset), (4 bytes).
    Removing u8log.o(i.u8log_SetRedrawMode), (4 bytes).
    Removing u8log.o(i.u8log_WriteChar), (40 bytes).
    Removing u8log.o(i.u8log_WriteDec16), (32 bytes).
    Removing u8log.o(i.u8log_WriteDec8), (32 bytes).
    Removing u8log.o(i.u8log_WriteHex16), (24 bytes).
    Removing u8log.o(i.u8log_WriteHex32), (24 bytes).
    Removing u8log.o(i.u8log_WriteHex8), (24 bytes).
    Removing u8log.o(i.u8log_WriteHexHalfByte), (42 bytes).
    Removing u8log.o(i.u8log_WriteString), (26 bytes).
    Removing u8log.o(i.u8log_clear_screen), (26 bytes).
    Removing u8log.o(i.u8log_cursor_on_screen), (46 bytes).
    Removing u8log.o(i.u8log_scroll_up), (72 bytes).
    Removing u8log.o(i.u8log_write_char), (120 bytes).
    Removing u8log.o(i.u8log_write_to_screen), (46 bytes).
    Removing u8log_u8g2.o(i.u8g2_DrawLog), (114 bytes).
    Removing u8log_u8g2.o(i.u8log_u8g2_cb), (50 bytes).
    Removing u8log_u8x8.o(i.u8log_u8x8_cb), (44 bytes).
    Removing u8log_u8x8.o(i.u8x8_DrawLog), (48 bytes).
    Removing u8log_u8x8.o(i.u8x8_DrawLogLine), (60 bytes).
    Removing u8x8_8x8.o(i.u8x8_Draw1x2Glyph), (96 bytes).
    Removing u8x8_8x8.o(i.u8x8_Draw1x2String), (36 bytes).
    Removing u8x8_8x8.o(i.u8x8_Draw1x2UTF8), (36 bytes).
    Removing u8x8_8x8.o(i.u8x8_Draw2x2Glyph), (104 bytes).
    Removing u8x8_8x8.o(i.u8x8_Draw2x2String), (36 bytes).
    Removing u8x8_8x8.o(i.u8x8_Draw2x2UTF8), (36 bytes).
    Removing u8x8_8x8.o(i.u8x8_DrawGlyph), (106 bytes).
    Removing u8x8_8x8.o(i.u8x8_DrawString), (36 bytes).
    Removing u8x8_8x8.o(i.u8x8_DrawUTF8), (36 bytes).
    Removing u8x8_8x8.o(i.u8x8_GetUTF8Len), (58 bytes).
    Removing u8x8_8x8.o(i.u8x8_SetFont), (4 bytes).
    Removing u8x8_8x8.o(i.u8x8_draw_1x2_string), (96 bytes).
    Removing u8x8_8x8.o(i.u8x8_draw_1x2_subglyph), (102 bytes).
    Removing u8x8_8x8.o(i.u8x8_draw_2x2_string), (104 bytes).
    Removing u8x8_8x8.o(i.u8x8_draw_2x2_subglyph), (172 bytes).
    Removing u8x8_8x8.o(i.u8x8_draw_string), (96 bytes).
    Removing u8x8_8x8.o(i.u8x8_get_glyph_data), (202 bytes).
    Removing u8x8_8x8.o(i.u8x8_upscale_buf), (28 bytes).
    Removing u8x8_8x8.o(i.u8x8_upscale_byte), (42 bytes).
    Removing u8x8_8x8.o(i.u8x8_utf8_next), (156 bytes).
    Removing u8x8_byte.o(i.i2c_clear_scl), (16 bytes).
    Removing u8x8_byte.o(i.i2c_clear_sda), (16 bytes).
    Removing u8x8_byte.o(i.i2c_delay), (18 bytes).
    Removing u8x8_byte.o(i.i2c_init), (32 bytes).
    Removing u8x8_byte.o(i.i2c_read_bit), (42 bytes).
    Removing u8x8_byte.o(i.i2c_read_scl_and_delay), (22 bytes).
    Removing u8x8_byte.o(i.i2c_read_sda), (16 bytes).
    Removing u8x8_byte.o(i.i2c_start), (60 bytes).
    Removing u8x8_byte.o(i.i2c_stop), (42 bytes).
    Removing u8x8_byte.o(i.i2c_write_bit), (42 bytes).
    Removing u8x8_byte.o(i.i2c_write_byte), (94 bytes).
    Removing u8x8_byte.o(i.u8x8_byte_3wire_sw_spi), (304 bytes).
    Removing u8x8_byte.o(i.u8x8_byte_4wire_sw_spi), (288 bytes).
    Removing u8x8_byte.o(i.u8x8_byte_8bit_6800mode), (248 bytes).
    Removing u8x8_byte.o(i.u8x8_byte_8bit_8080mode), (248 bytes).
    Removing u8x8_byte.o(i.u8x8_byte_SetDC), (20 bytes).
    Removing u8x8_byte.o(i.u8x8_byte_empty), (46 bytes).
    Removing u8x8_byte.o(i.u8x8_byte_ks0108), (240 bytes).
    Removing u8x8_byte.o(i.u8x8_byte_sed1520), (240 bytes).
    Removing u8x8_byte.o(i.u8x8_byte_set_ks0108_cs), (48 bytes).
    Removing u8x8_byte.o(i.u8x8_byte_sw_i2c), (110 bytes).
    Removing u8x8_byte.o(.data), (2 bytes).
    Removing u8x8_cad.o(i.u8x8_SendF), (30 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_001), (108 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_011), (108 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_011_ssd13xx_i2c), (174 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_100), (108 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_110), (108 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_SendMultipleArg), (38 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_empty), (84 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_ld7032_i2c), (208 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_ssd13xx_i2c), (146 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_st75256_i2c), (174 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_st7920_spi), (288 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_uc1638_i2c), (396 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_uc16xx_i2c), (308 bytes).
    Removing u8x8_cad.o(i.u8x8_cad_vsendf), (100 bytes).
    Removing u8x8_cad.o(i.u8x8_gu800_cad_110), (166 bytes).
    Removing u8x8_cad.o(.bss), (16 bytes).
    Removing u8x8_capture.o(i.u8x8_capture_get_pixel_1), (44 bytes).
    Removing u8x8_capture.o(i.u8x8_capture_get_pixel_2), (38 bytes).
    Removing u8x8_capture.o(i.u8x8_capture_write_pbm_buffer), (100 bytes).
    Removing u8x8_capture.o(i.u8x8_capture_write_pbm_pre), (56 bytes).
    Removing u8x8_capture.o(i.u8x8_capture_write_xbm_buffer), (268 bytes).
    Removing u8x8_capture.o(i.u8x8_capture_write_xbm_pre), (140 bytes).
    Removing u8x8_debounce.o(i.u8x8_GetMenuEvent), (150 bytes).
    Removing u8x8_debounce.o(i.u8x8_find_first_diff), (44 bytes).
    Removing u8x8_debounce.o(i.u8x8_read_pin_state), (56 bytes).
    Removing u8x8_display.o(i.u8x8_ClearDisplay), (20 bytes).
    Removing u8x8_display.o(i.u8x8_ClearDisplayWithTile), (72 bytes).
    Removing u8x8_display.o(i.u8x8_ClearLine), (56 bytes).
    Removing u8x8_display.o(i.u8x8_FillDisplay), (32 bytes).
    Removing u8x8_display.o(i.u8x8_InitInterface), (30 bytes).
    Removing u8x8_display.o(i.u8x8_SetContrast), (20 bytes).
    Removing u8x8_display.o(i.u8x8_SetFlipMode), (20 bytes).
    Removing u8x8_fonts.o(.constdata), (487478 bytes).
    Removing u8x8_input_value.o(i.u8x8_UserInterfaceInputValue), (372 bytes).
    Removing u8x8_message.o(i.u8x8_UserInterfaceMessage), (298 bytes).
    Removing u8x8_message.o(i.u8x8_draw_button_line), (188 bytes).
    Removing u8x8_selection_list.o(i.u8sl_Next), (48 bytes).
    Removing u8x8_selection_list.o(i.u8sl_Prev), (52 bytes).
    Removing u8x8_selection_list.o(i.u8x8_DrawSelectionList), (44 bytes).
    Removing u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList), (244 bytes).
    Removing u8x8_selection_list.o(i.u8x8_sl_string_line_cb), (100 bytes).
    Removing u8x8_setup.o(i.u8x8_d_null_cb), (52 bytes).
    Removing u8x8_setup.o(.constdata), (24 bytes).
    Removing u8x8_string.o(i.u8x8_CopyStringLine), (58 bytes).
    Removing u8x8_string.o(i.u8x8_DrawUTF8Line), (136 bytes).
    Removing u8x8_string.o(i.u8x8_DrawUTF8Lines), (68 bytes).
    Removing u8x8_string.o(i.u8x8_GetStringLineCnt), (36 bytes).
    Removing u8x8_string.o(i.u8x8_GetStringLineStart), (46 bytes).
    Removing u8x8_u8toa.o(i.u8x8_s8toa), (84 bytes).
    Removing u8x8_u8toa.o(i.u8x8_u8toa), (28 bytes).
    Removing u8x8_u8toa.o(i.u8x8_u8toap), (48 bytes).
    Removing u8x8_u8toa.o(i.u8x8_u8tox), (100 bytes).
    Removing u8x8_u8toa.o(.constdata), (3 bytes).
    Removing u8x8_u8toa.o(.data), (12 bytes).
    Removing u8x8_u16toa.o(i.u8x8_u16toa), (28 bytes).
    Removing u8x8_u16toa.o(i.u8x8_u16toap), (50 bytes).
    Removing u8x8_u16toa.o(i.u8x8_utoa), (34 bytes).
    Removing u8x8_u16toa.o(.data), (6 bytes).
    Removing fadd.o(.text), (176 bytes).
    Removing fmul.o(.text), (100 bytes).
    Removing ffltui.o(.text), (10 bytes).
    Removing ffixui.o(.text), (40 bytes).
    Removing cfcmple.o(.text), (20 bytes).
    Removing fepilogue.o(.text), (110 bytes).

687 unused section(s) (total 551967 bytes) removed from the image.
